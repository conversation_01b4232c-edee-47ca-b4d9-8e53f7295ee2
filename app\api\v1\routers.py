from fastapi import APIRouter
from app.api.v1.endpoints import (
    auth, 
    users, 
    properties, 
    investments, 
    search,
    services
)

api_router = APIRouter()

api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(properties.router, prefix="/properties", tags=["properties"])
api_router.include_router(investments.router, prefix="/investments", tags=["investments"])
api_router.include_router(search.router, prefix="/search", tags=["search"])
api_router.include_router(services.router, prefix="/services", tags=["services"])