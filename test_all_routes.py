#!/usr/bin/env python3
"""
Test all routes and endpoints
"""

import requests
import json

def test_all_routes():
    """Test all application routes"""
    
    print("🌐 Testing All Routes")
    print("=" * 40)
    
    routes = [
        {"url": "/", "name": "Homepage"},
        {"url": "/properties", "name": "Properties Page"},
        {"url": "/dashboard", "name": "Dashboard Page"},
        {"url": "/investments", "name": "Investments Page"},
        {"url": "/services", "name": "Services Page"},
        {"url": "/about", "name": "About Page"},
    ]
    
    base_url = "http://127.0.0.1:8000"
    all_good = True
    
    for route in routes:
        try:
            response = requests.get(f"{base_url}{route['url']}")
            status = "✅ OK" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"{route['name']:<20} {route['url']:<15} {status}")
            
            if response.status_code != 200:
                all_good = False
                print(f"   Error: {response.text[:100]}...")
                
        except Exception as e:
            print(f"{route['name']:<20} {route['url']:<15} ❌ ERROR: {str(e)}")
            all_good = False
    
    return all_good

def test_static_files():
    """Test static file serving"""
    
    print("\n📁 Testing Static Files")
    print("=" * 40)
    
    static_files = [
        {"url": "/static/css/main.css", "name": "Main CSS"},
        {"url": "/static/css/components.css", "name": "Components CSS"},
        {"url": "/static/css/responsive.css", "name": "Responsive CSS"},
        {"url": "/static/js/main.js", "name": "Main JS"},
        {"url": "/static/js/config.js", "name": "Config JS"},
        {"url": "/static/js/auth.js", "name": "Auth JS"},
        {"url": "/static/js/api.js", "name": "API JS"},
        {"url": "/static/js/properties.js", "name": "Properties JS"},
        {"url": "/static/js/dashboard.js", "name": "Dashboard JS"},
    ]
    
    base_url = "http://127.0.0.1:8000"
    all_good = True
    
    for file in static_files:
        try:
            response = requests.get(f"{base_url}{file['url']}")
            status = "✅ OK" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"{file['name']:<20} {file['url']:<30} {status}")
            
            if response.status_code != 200:
                all_good = False
                
        except Exception as e:
            print(f"{file['name']:<20} {file['url']:<30} ❌ ERROR: {str(e)}")
            all_good = False
    
    return all_good

def test_api_endpoints():
    """Test API endpoints"""
    
    print("\n🔌 Testing API Endpoints")
    print("=" * 40)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test API docs
    try:
        response = requests.get(f"{base_url}/api/docs")
        status = "✅ OK" if response.status_code == 200 else f"❌ {response.status_code}"
        print(f"{'API Docs':<20} {'/api/docs':<25} {status}")
    except Exception as e:
        print(f"{'API Docs':<20} {'/api/docs':<25} ❌ ERROR: {str(e)}")
    
    # Test auth endpoints (expect specific error codes)
    auth_tests = [
        {"url": "/api/v1/auth/register", "method": "POST", "expected": [422, 400], "name": "Register"},
        {"url": "/api/v1/auth/login", "method": "POST", "expected": [422, 401], "name": "Login"},
        {"url": "/api/v1/users/me", "method": "GET", "expected": [401, 403], "name": "User Profile"},
    ]
    
    for test in auth_tests:
        try:
            if test["method"] == "POST":
                response = requests.post(f"{base_url}{test['url']}", json={})
            else:
                response = requests.get(f"{base_url}{test['url']}")
            
            if response.status_code in test["expected"]:
                status = f"✅ {response.status_code} (Expected)"
            else:
                status = f"❌ {response.status_code} (Unexpected)"
            
            print(f"{test['name']:<20} {test['url']:<25} {status}")
            
        except Exception as e:
            print(f"{test['name']:<20} {test['url']:<25} ❌ ERROR: {str(e)}")

def check_server_health():
    """Check overall server health"""
    
    print("\n🏥 Server Health Check")
    print("=" * 40)
    
    try:
        # Check if server is responding
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        
        if response.status_code == 200:
            print("✅ Server is running and responding")
            print(f"   Response time: {response.elapsed.total_seconds():.2f}s")
            print(f"   Content length: {len(response.content)} bytes")
            return True
        else:
            print(f"❌ Server responding with status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server!")
        print("   Make sure the server is running:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ Server is responding too slowly")
        return False
    except Exception as e:
        print(f"❌ Server health check failed: {str(e)}")
        return False

def diagnose_issues():
    """Provide diagnostic information"""
    
    print("\n🔧 Diagnostic Information")
    print("=" * 40)
    
    # Check if templates exist
    import os
    template_files = [
        "app/templates/index.html",
        "app/templates/base.html",
        "app/templates/properties.html",
        "app/templates/dashboard.html"
    ]
    
    print("📄 Template Files:")
    for template in template_files:
        exists = "✅" if os.path.exists(template) else "❌"
        print(f"   {exists} {template}")
    
    # Check if static files exist
    static_files = [
        "app/static/css/main.css",
        "app/static/js/main.js",
        "app/static/js/config.js"
    ]
    
    print("\n📁 Static Files:")
    for static_file in static_files:
        exists = "✅" if os.path.exists(static_file) else "❌"
        print(f"   {exists} {static_file}")
    
    print(f"\n📍 Current Directory: {os.getcwd()}")

if __name__ == "__main__":
    print("🚀 DreamBig Complete Route Test")
    print("=" * 60)
    
    # Check server health first
    if not check_server_health():
        print("\n❌ Server is not running or not accessible!")
        exit(1)
    
    # Run all tests
    routes_ok = test_all_routes()
    static_ok = test_static_files()
    test_api_endpoints()
    
    # Provide diagnostic info
    diagnose_issues()
    
    print("\n" + "=" * 60)
    if routes_ok and static_ok:
        print("🎉 All routes and static files are working!")
        print("\n💡 If you're still getting 'Not Found' errors:")
        print("   1. Clear your browser cache (Ctrl+F5)")
        print("   2. Try incognito/private browsing mode")
        print("   3. Check the exact URL you're visiting")
        print("   4. Look at browser developer tools (F12) for errors")
        print("   5. Check server logs for error messages")
    else:
        print("❌ Some routes or files are not working!")
        print("   Check the errors above and fix them.")
    
    print(f"\n🌐 Visit: http://127.0.0.1:8000/")
    print(f"📚 API Docs: http://127.0.0.1:8000/api/docs")
