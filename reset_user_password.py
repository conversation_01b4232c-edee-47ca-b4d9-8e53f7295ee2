#!/usr/bin/env python3
"""
Tool to reset password for existing Firebase users
"""
import firebase_admin
from firebase_admin import credentials, auth
import sys

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        try:
            firebase_admin.get_app()
            return True
        except ValueError:
            pass
        
        cred = credentials.Certificate("app/dreambig_firebase_credentioal.json")
        firebase_admin.initialize_app(cred)
        print("✓ Firebase initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ Firebase initialization failed: {e}")
        return False

def reset_password(email, new_password):
    """Reset password for a user"""
    try:
        # Get user by email
        user = auth.get_user_by_email(email)
        
        # Update password
        auth.update_user(
            user.uid,
            password=new_password,
            email_verified=True  # Also verify email
        )
        
        print(f"✓ Password reset successful for {email}")
        print(f"New password: {new_password}")
        print(f"Email verified: True")
        return True
        
    except auth.UserNotFoundError:
        print(f"✗ User {email} not found")
        return False
    except Exception as e:
        print(f"✗ Error resetting password: {e}")
        return False

def main():
    if not initialize_firebase():
        return
    
    print("🔑 Firebase Password Reset Tool")
    print("=" * 40)
    
    # Common users from your Firebase
    users = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    print("Available users:")
    for i, user in enumerate(users, 1):
        print(f"{i}. {user}")
    
    try:
        choice = input("\nSelect user number (or enter custom email): ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(users):
            email = users[int(choice) - 1]
        else:
            email = choice
        
        new_password = input("Enter new password (min 6 chars): ").strip()
        
        if len(new_password) < 6:
            print("✗ Password must be at least 6 characters")
            return
        
        if reset_password(email, new_password):
            print(f"\n🎉 Success! You can now login with:")
            print(f"Email: {email}")
            print(f"Password: {new_password}")
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
