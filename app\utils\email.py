from fastapi import BackgroundTasks
from app.config import settings
from typing import List, Optional
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

async def send_email(
    background_tasks: BackgroundTasks,
    email_to: str,
    subject: str,
    body: str,
    html: Optional[str] = None
):
    """Send email using SMTP in background"""
    if not all([settings.SMTP_HOST, settings.SMTP_PORT, settings.SMTP_USER, settings.SMTP_PASSWORD]):
        raise RuntimeError("SMTP configuration not set")

    message = MIMEMultipart()
    message["From"] = settings.EMAILS_FROM_EMAIL # type: ignore
    message["To"] = email_to
    message["Subject"] = subject

    message.attach(MIMEText(body, "plain"))
    if html:
        message.attach(MIMEText(html, "html"))

    def _send():
        with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server: # type: ignore
            server.starttls()
            server.login(settings.SMTP_USER, settings.SMTP_PASSWORD) # type: ignore
            server.send_message(message)

    background_tasks.add_task(_send)