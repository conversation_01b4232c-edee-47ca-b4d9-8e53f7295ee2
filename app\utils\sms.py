from fastapi import BackgroundTasks
from typing import List
import requests
from app.config import settings

async def send_sms(
    background_tasks: BackgroundTasks,
    phone_numbers: List[str],
    message: str
):
    """Send SMS using external service (mock implementation)"""
    def _send():
        # In production, integrate with SMS gateway like <PERSON>wilio
        for number in phone_numbers:
            print(f"Sending SMS to {number}: {message}")
            # Example with Twilio:
            # from twilio.rest import Client
            # client = Client(settings.TWILIO_SID, settings.TWILIO_TOKEN)
            # client.messages.create(
            #     body=message,
            #     from_=settings.TWILIO_NUMBER,
            #     to=number
            # )

    background_tasks.add_task(_send)