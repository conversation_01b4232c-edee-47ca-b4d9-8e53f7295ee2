<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Verification - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .kyc-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .kyc-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .kyc-header h1 {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .kyc-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .kyc-form {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: #667eea;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .file-upload {
            border: 2px dashed #e1e5e9;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .file-upload:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background: #f0f2ff;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.9rem;
            color: #999;
        }

        .file-preview {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .file-preview.show {
            display: block;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-info i {
            color: #667eea;
        }

        .verification-status {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            display: none;
        }

        .verification-status.show {
            display: block;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .status-item.pending {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .status-item.success {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        .status-item.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }

        .fraud-score {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            display: none;
        }

        .fraud-score.show {
            display: block;
        }

        .score-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 2rem;
            font-weight: 700;
            color: white;
        }

        .score-circle.low {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .score-circle.medium {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .score-circle.high {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
        }

        .score-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
        }

        .score-detail {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .score-detail h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .score-detail p {
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .kyc-container {
                padding: 15px;
            }

            .kyc-form {
                padding: 25px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="kyc-container">
        <!-- KYC Header -->
        <div class="kyc-header">
            <h1>KYC Verification</h1>
            <p>Complete your identity verification to access all platform features</p>
        </div>

        <!-- Verification Status -->
        <div class="verification-status" id="verificationStatus">
            <h2>
                <i class="fas fa-shield-alt"></i>
                Verification Status
            </h2>
            <div id="statusItems"></div>
        </div>

        <!-- KYC Form -->
        <div class="kyc-form">
            <form id="kycForm">
                <!-- Personal Information -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        Personal Information
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="fullName">Full Name (as per ID)</label>
                            <input type="text" id="fullName" name="fullName" required>
                            <span class="error-message" id="fullNameError"></span>
                        </div>
                        <div class="form-group">
                            <label for="dateOfBirth">Date of Birth</label>
                            <input type="date" id="dateOfBirth" name="dateOfBirth" required>
                            <span class="error-message" id="dateOfBirthError"></span>
                        </div>
                        <div class="form-group">
                            <label for="nationality">Nationality</label>
                            <select id="nationality" name="nationality" required>
                                <option value="">Select Nationality</option>
                                <option value="indian">Indian</option>
                                <option value="other">Other</option>
                            </select>
                            <span class="error-message" id="nationalityError"></span>
                        </div>
                        <div class="form-group">
                            <label for="occupation">Occupation</label>
                            <input type="text" id="occupation" name="occupation" required>
                            <span class="error-message" id="occupationError"></span>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-map-marker-alt"></i>
                        Address Information
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="address">Complete Address</label>
                            <textarea id="address" name="address" rows="3" required></textarea>
                            <span class="error-message" id="addressError"></span>
                        </div>
                        <div class="form-group">
                            <label for="city">City</label>
                            <input type="text" id="city" name="city" required>
                            <span class="error-message" id="cityError"></span>
                        </div>
                        <div class="form-group">
                            <label for="state">State</label>
                            <input type="text" id="state" name="state" required>
                            <span class="error-message" id="stateError"></span>
                        </div>
                        <div class="form-group">
                            <label for="pincode">PIN Code</label>
                            <input type="text" id="pincode" name="pincode" pattern="[0-9]{6}" required>
                            <span class="error-message" id="pincodeError"></span>
                        </div>
                    </div>
                </div>

                <!-- Document Upload -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-file-upload"></i>
                        Document Upload
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Government ID (Aadhaar/PAN/Passport)</label>
                            <div class="file-upload" onclick="document.getElementById('govId').click()">
                                <input type="file" id="govId" name="govId" accept=".jpg,.jpeg,.png,.pdf" required>
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">Click to upload or drag and drop</div>
                                <div class="upload-hint">JPG, PNG or PDF (max 5MB)</div>
                                <div class="file-preview" id="govIdPreview">
                                    <div class="file-info">
                                        <i class="fas fa-file"></i>
                                        <span id="govIdName"></span>
                                    </div>
                                </div>
                            </div>
                            <span class="error-message" id="govIdError"></span>
                        </div>

                        <div class="form-group">
                            <label>Address Proof (Utility Bill/Bank Statement)</label>
                            <div class="file-upload" onclick="document.getElementById('addressProof').click()">
                                <input type="file" id="addressProof" name="addressProof" accept=".jpg,.jpeg,.png,.pdf" required>
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">Click to upload or drag and drop</div>
                                <div class="upload-hint">JPG, PNG or PDF (max 5MB)</div>
                                <div class="file-preview" id="addressProofPreview">
                                    <div class="file-info">
                                        <i class="fas fa-file"></i>
                                        <span id="addressProofName"></span>
                                    </div>
                                </div>
                            </div>
                            <span class="error-message" id="addressProofError"></span>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="auth-button" id="kycSubmitButton">
                    <span class="button-text">Submit for Verification</span>
                    <div class="loading-spinner" id="kycSpinner"></div>
                </button>
            </form>
        </div>

        <!-- Fraud Score Display -->
        <div class="fraud-score" id="fraudScore">
            <div class="score-header">
                <h2>AI Fraud Detection Analysis</h2>
                <div class="score-circle" id="scoreCircle">
                    <span id="scoreValue">0</span>
                </div>
                <p id="scoreDescription">Analysis complete</p>
            </div>
            <div class="score-details" id="scoreDetails"></div>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/kyc.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = '/login';
                return;
            }

            initializeKYC();
        });
    </script>
</body>
</html>
