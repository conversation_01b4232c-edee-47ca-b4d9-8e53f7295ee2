#!/usr/bin/env python3
"""
Test script to verify database functionality
"""

from sqlalchemy.orm import Session
from app.db.session import get_db, engine
from app.db import models, crud
from app.db.models import User<PERSON>ole

def test_database_connection():
    """Test database connection and table creation"""
    print("🔍 Testing Database Connection...")
    
    try:
        # Test connection
        with engine.connect() as connection:
            print("✅ Database connection successful")
            
        # Test table creation
        models.Base.metadata.create_all(bind=engine)
        print("✅ Database tables created/verified")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def test_user_creation():
    """Test user creation in database"""
    print("\n🧪 Testing User Creation...")
    
    try:
        # Get database session
        db = next(get_db())
        
        # Test user data
        test_user_data = {
            'firebase_uid': 'test_uid_12345',
            'email': '<EMAIL>',
            'phone': '9876543210',
            'name': 'Test Database User',
            'role': UserRole.TENANT,
            'is_active': True,
            'kyc_verified': False
        }
        
        print(f"📝 Creating user with data: {test_user_data}")
        
        # Try to create user
        db_user = crud.create_user(db, test_user_data)
        
        print(f"✅ User created successfully!")
        print(f"   ID: {db_user.id}")
        print(f"   Email: {db_user.email}")
        print(f"   Firebase UID: {db_user.firebase_uid}")
        print(f"   Role: {db_user.role}")
        
        # Clean up - delete the test user
        db.delete(db_user)
        db.commit()
        print("🧹 Test user cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ User creation failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        
        # Try to rollback
        try:
            db.rollback()
        except:
            pass
            
        return False
    finally:
        try:
            db.close()
        except:
            pass

def test_user_retrieval():
    """Test user retrieval functions"""
    print("\n🔍 Testing User Retrieval...")
    
    try:
        db = next(get_db())
        
        # Create a test user first
        test_user_data = {
            'firebase_uid': 'test_retrieve_uid',
            'email': '<EMAIL>',
            'phone': '9876543211',
            'name': 'Test Retrieve User',
            'role': UserRole.TENANT,
            'is_active': True,
            'kyc_verified': False
        }
        
        db_user = crud.create_user(db, test_user_data)
        print(f"✅ Test user created for retrieval test")
        
        # Test retrieval by Firebase UID
        retrieved_user = crud.get_user_by_firebase_uid(db, 'test_retrieve_uid')
        if retrieved_user:
            print(f"✅ User retrieved by Firebase UID: {retrieved_user.email}")
        else:
            print(f"❌ Failed to retrieve user by Firebase UID")
            
        # Test retrieval by email
        retrieved_user_email = crud.get_user_by_email(db, '<EMAIL>')
        if retrieved_user_email:
            print(f"✅ User retrieved by email: {retrieved_user_email.firebase_uid}")
        else:
            print(f"❌ Failed to retrieve user by email")
        
        # Clean up
        db.delete(db_user)
        db.commit()
        print("🧹 Test user cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ User retrieval test failed: {str(e)}")
        try:
            db.rollback()
        except:
            pass
        return False
    finally:
        try:
            db.close()
        except:
            pass

def check_table_structure():
    """Check if all required tables exist"""
    print("\n🏗️ Checking Table Structure...")
    
    try:
        from sqlalchemy import inspect
        
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        required_tables = ['users', 'properties', 'favorites', 'recently_viewed', 
                          'investments', 'service_bookings', 'notifications']
        
        print(f"📋 Found tables: {tables}")
        
        for table in required_tables:
            if table in tables:
                print(f"✅ Table '{table}' exists")
                
                # Check columns for users table
                if table == 'users':
                    columns = inspector.get_columns(table)
                    column_names = [col['name'] for col in columns]
                    print(f"   Columns: {column_names}")
            else:
                print(f"❌ Table '{table}' missing")
                
        return True
        
    except Exception as e:
        print(f"❌ Table structure check failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 DreamBig Database Test")
    print("=" * 50)
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Database connection failed. Please check your database configuration.")
        exit(1)
    
    # Check table structure
    check_table_structure()
    
    # Test user creation
    if test_user_creation():
        print("\n✅ User creation test passed!")
    else:
        print("\n❌ User creation test failed!")
        
    # Test user retrieval
    if test_user_retrieval():
        print("\n✅ User retrieval test passed!")
    else:
        print("\n❌ User retrieval test failed!")
    
    print("\n🎉 Database tests completed!")
    print("\n💡 If tests failed, check:")
    print("   1. Database is running and accessible")
    print("   2. Database tables are created properly")
    print("   3. User model fields match database schema")
    print("   4. No unique constraint violations")
