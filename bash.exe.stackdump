Stack trace:
Frame         Function      Args
0007FFFF8B70  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7A70) msys-2.0.dll+0x2118E
0007FFFF8B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF8B70  0002100469F2 (00021028DF99, 0007FFFF8A28, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8B70  00021006A545 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF8B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC44D60000 ntdll.dll
7FFC42EF0000 KERNEL32.DLL
7FFC42710000 KERNELBASE.dll
7FFC43490000 USER32.dll
7FFC426E0000 win32u.dll
7FFC433D0000 GDI32.dll
7FFC423C0000 gdi32full.dll
7FFC42030000 msvcp_win.dll
7FFC42590000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC430E0000 advapi32.dll
7FFC436C0000 msvcrt.dll
7FFC437C0000 sechost.dll
7FFC42FC0000 RPCRT4.dll
7FFC414B0000 CRYPTBASE.DLL
7FFC42320000 bcryptPrimitives.dll
7FFC42EB0000 IMM32.DLL
