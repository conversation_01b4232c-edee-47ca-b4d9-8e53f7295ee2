<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Properties - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .properties-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .properties-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .properties-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .properties-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .properties-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            max-width: 600px;
            margin: 0 auto;
        }

        .breadcrumb {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .search-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            position: relative;
        }

        .search-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 20px 20px 0 0;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 2;
            min-width: 300px;
            padding: 18px 25px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102,126,234,0.1);
            background: white;
        }

        .search-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 30px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .search-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.4);
        }

        .filters-toggle {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #e1e5e9;
            padding: 18px 30px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .filters-toggle:hover {
            background: #e9ecef;
            border-color: #adb5bd;
            transform: translateY(-2px);
        }

        .filters-section {
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background: #f8f9fa;
            border-radius: 15px;
            margin-top: 20px;
        }

        .filters-section.active {
            max-height: 500px;
            padding: 30px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item i {
            color: #667eea;
            width: 16px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-count {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .view-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .sort-dropdown {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }

        .view-toggle {
            display: flex;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .view-toggle button {
            padding: 8px 12px;
            border: none;
            background: white;
            cursor: pointer;
            border-right: 1px solid #ddd;
        }

        .view-toggle button:last-child {
            border-right: none;
        }

        .view-toggle button.active {
            background: #007bff;
            color: white;
        }

        .main-content {
            display: flex;
            gap: 25px;
            align-items: flex-start;
        }

        .filters-sidebar {
            width: 300px;
            flex-shrink: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 25px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .properties-main {
            flex: 1;
            min-width: 0;
        }

        .filter-section {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .filter-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e5e9;
            font-weight: 600;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .filter-content {
            padding: 20px;
        }

        .filter-content.collapsed {
            display: none;
        }

        .applied-filters {
            margin-bottom: 20px;
        }

        .applied-filters h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .filter-tag {
            display: inline-flex;
            align-items: center;
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            margin: 2px;
            gap: 5px;
        }

        .filter-tag .remove {
            cursor: pointer;
            font-weight: bold;
        }

        .clear-all {
            color: #007bff;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .clear-all:hover {
            text-decoration: underline;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-group:last-child {
            margin-bottom: 0;
        }

        .filter-group label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .filter-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .filter-option input[type="checkbox"],
        .filter-option input[type="radio"] {
            margin: 0;
        }

        .filter-option label {
            margin: 0;
            font-weight: normal;
            cursor: pointer;
            flex: 1;
        }

        .price-range {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .price-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .properties-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .property-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            border: 1px solid #f0f0f0;
        }

        .property-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .property-image {
            width: 100%;
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
            overflow: hidden;
        }

        .property-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.1) 100%);
            z-index: 1;
        }

        .property-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: capitalize;
            z-index: 2;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .property-favorite {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.95);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .property-favorite:hover {
            background: white;
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .property-favorite.active {
            color: #ff4757;
            background: #fff5f5;
        }

        .property-content {
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .property-header {
            margin-bottom: 10px;
        }

        .property-society {
            font-size: 0.85rem;
            color: #666;
            margin-bottom: 2px;
        }

        .property-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .property-location {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .property-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .property-specs {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .property-spec {
            font-size: 0.85rem;
            color: #666;
        }

        .property-price {
            text-align: right;
        }

        .price-amount {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 2px;
        }

        .price-per-sqft {
            font-size: 0.8rem;
            color: #666;
        }

        .property-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
        }

        .property-status {
            font-size: 0.8rem;
            color: #28a745;
            font-weight: 500;
        }

        .property-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #007bff;
            color: white;
        }

        .action-btn:hover {
            background: #007bff;
            color: white;
        }

        .property-main-info {
            flex: 1;
        }

        .property-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .property-location {
            color: #7f8c8d;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1rem;
            font-weight: 500;
        }

        .property-location i {
            color: #667eea;
            font-size: 0.9rem;
        }

        .property-description {
            color: #555;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .property-specs {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .property-spec {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #555;
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 20px;
            border: 1px solid #e9ecef;
            font-weight: 500;
        }

        .property-spec i {
            color: #667eea;
            font-size: 0.85rem;
        }

        .property-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #f8f9fa;
        }

        .property-price {
            font-size: 1.6rem;
            font-weight: 800;
            color: #2c3e50;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .price-per-sqft {
            font-size: 0.8rem;
            color: #7f8c8d;
            font-weight: 400;
        }

        .property-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            padding: 12px 20px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102,126,234,0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102,126,234,0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 1px solid #e1e5e9;
        }

        .btn-secondary.active {
            background: #ff4757;
            border-color: #ff4757;
            color: white;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .property-card {
                flex-direction: column;
                min-height: auto;
            }

            .property-image {
                width: 100%;
                height: 200px;
                min-width: auto;
            }

            .property-content {
                padding: 20px;
            }

            .property-specs {
                flex-wrap: wrap;
                gap: 10px;
            }

            .property-bottom {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .property-actions {
                width: 100%;
                justify-content: space-between;
            }

            .action-btn {
                flex: 1;
                text-align: center;
            }

            .search-bar {
                flex-direction: column;
                gap: 10px;
            }

            .search-input {
                min-width: auto;
                width: 100%;
            }

            .search-button,
            .filters-toggle {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .properties-container {
                padding: 10px;
            }

            .property-title {
                font-size: 1.2rem;
            }

            .property-price {
                font-size: 1.3rem;
            }

            .property-specs {
                gap: 8px;
            }

            .property-spec {
                font-size: 0.8rem;
                padding: 4px 8px;
            }
        }

        .property-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-favorite {
            background: #ff4757;
            color: white;
        }

        .btn-favorite.active {
            background: #ff3742;
        }

        .action-button:hover {
            transform: translateY(-2px);
        }

        .loading-message {
            text-align: center;
            padding: 60px;
            color: #666;
            font-size: 1.1rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .empty-message {
            text-align: center;
            padding: 60px;
            color: #666;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .empty-message i {
            font-size: 3rem;
            color: #ccc;
            margin-bottom: 20px;
            display: block;
        }

        .empty-message h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .empty-message p {
            margin-bottom: 20px;
        }

        .empty-message .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .empty-message .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover,
        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .add-property-fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 65px;
            height: 65px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 8px 30px rgba(102,126,234,0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .add-property-fab:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 12px 40px rgba(102,126,234,0.5);
        }

        .loading-message, .empty-message {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }

        .loading-message {
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .loading-message::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .empty-message {
            background: white;
            border-radius: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            margin: 20px 0;
        }

        .empty-message i {
            font-size: 4rem;
            color: #e9ecef;
            margin-bottom: 20px;
        }

        .empty-message h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .empty-message p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 10px 10px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .properties-grid {
                grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .properties-container {
                padding: 15px;
            }

            .properties-header {
                padding: 40px 20px;
                margin-bottom: 20px;
            }

            .properties-header h1 {
                font-size: 2.2rem;
            }

            .properties-header p {
                font-size: 1rem;
            }

            .search-section {
                padding: 25px 20px;
                margin-bottom: 25px;
            }

            .search-bar {
                flex-direction: column;
                gap: 15px;
            }

            .search-input {
                min-width: auto;
                width: 100%;
                padding: 15px 20px;
            }

            .search-button,
            .filters-toggle {
                width: 100%;
                padding: 15px 25px;
            }

            .filters-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .properties-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 20px;
            }

            .property-image {
                height: 220px;
            }

            .property-content {
                padding: 20px;
            }

            .property-specs {
                flex-wrap: wrap;
                gap: 8px;
            }

            .property-spec {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .property-actions {
                flex-direction: column;
                gap: 10px;
            }

            .action-btn {
                width: 100%;
                justify-content: center;
            }

            .results-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .sort-dropdown {
                width: 100%;
                padding: 12px 15px;
            }

            .add-property-fab {
                width: 55px;
                height: 55px;
                bottom: 20px;
                right: 20px;
            }
        }

        @media (max-width: 480px) {
            .properties-header h1 {
                font-size: 1.8rem;
            }

            .search-section {
                padding: 20px 15px;
            }

            .property-title {
                font-size: 1.1rem;
            }

            .property-price {
                font-size: 1.4rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="properties-container">
        <!-- Properties Header -->
        <div class="properties-header">
            <h1>Find Your Perfect Property</h1>
            <p>Discover thousands of verified properties with AI-powered recommendations</p>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <div class="search-bar">
                <div style="position: relative; flex: 2;">
                    <input type="text" class="search-input" id="searchInput"
                           placeholder="Search by location, property type, or keywords...">
                    <div class="suggestions-dropdown" id="suggestionsDropdown"></div>
                </div>
                <button class="search-button" onclick="searchProperties()">
                    <i class="fas fa-search"></i> Search
                </button>
                <button class="filters-toggle" onclick="toggleFilters()">
                    <i class="fas fa-filter"></i> Filters
                </button>
            </div>

            <div class="filters-section" id="filtersSection">
                <div class="filters-grid">
                <div class="filter-group">
                    <label>Property Type</label>
                    <select id="propertyType">
                        <option value="">All Types</option>
                        <option value="apartment">Apartment</option>
                        <option value="house">House</option>
                        <option value="villa">Villa</option>
                        <option value="commercial">Commercial</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>BHK</label>
                    <select id="bhkFilter">
                        <option value="">Any BHK</option>
                        <option value="1">1 BHK</option>
                        <option value="2">2 BHK</option>
                        <option value="3">3 BHK</option>
                        <option value="4">4+ BHK</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Min Price (₹)</label>
                    <input type="number" id="minPrice" placeholder="Min Price">
                </div>

                <div class="filter-group">
                    <label>Max Price (₹)</label>
                    <input type="number" id="maxPrice" placeholder="Max Price">
                </div>

                <div class="filter-group">
                    <label>Furnishing</label>
                    <select id="furnishing">
                        <option value="">Any</option>
                        <option value="furnished">Furnished</option>
                        <option value="semi_furnished">Semi-Furnished</option>
                        <option value="unfurnished">Unfurnished</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label>Location</label>
                    <input type="text" id="locationFilter" placeholder="City or Area">
                </div>
                </div>
            </div>
        </div>

        <!-- Results Header -->
        <div class="results-header" id="resultsHeader" style="display: none;">
            <div class="results-count" id="resultsCount">
                <i class="fas fa-home"></i>
                <span id="propertyCount">0</span> properties found
            </div>
            <div class="view-controls">
                <select class="sort-dropdown" id="sortDropdown" onchange="sortProperties()">
                    <option value="price_asc">Price: Low to High</option>
                    <option value="price_desc">Price: High to Low</option>
                    <option value="area_desc">Area: Largest First</option>
                    <option value="newest">Newest First</option>
                    <option value="bhk_asc">BHK: Low to High</option>
                </select>
            </div>
        </div>

        <!-- Properties Grid -->
        <div class="properties-grid" id="propertiesGrid">
            <div class="loading-message">
                <span>Loading amazing properties for you...</span>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination" id="pagination" style="display: none;"></div>

        <!-- Add Property FAB -->
        <button class="add-property-fab" onclick="addProperty()" title="Add New Property">
            <i class="fas fa-plus"></i>
        </button>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/properties.js') }}"></script>
    
    <script>
        // Toggle filters visibility
        function toggleFilters() {
            const filtersSection = document.getElementById('filtersSection');
            const toggleButton = document.querySelector('.filters-toggle');

            filtersSection.classList.toggle('active');

            if (filtersSection.classList.contains('active')) {
                toggleButton.innerHTML = '<i class="fas fa-times"></i> Hide Filters';
            } else {
                toggleButton.innerHTML = '<i class="fas fa-filter"></i> Filters';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeProperties();
        });
    </script>
</body>
</html>
