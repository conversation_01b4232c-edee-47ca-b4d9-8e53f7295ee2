#!/usr/bin/env python3
"""
Debug script to test Firebase connection and list users
"""
import firebase_admin
from firebase_admin import credentials, auth
import json

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if already initialized
        try:
            firebase_admin.get_app()
            print("✓ Firebase app already initialized")
            return True
        except ValueError:
            pass
        
        # Initialize with credentials
        cred = credentials.Certificate("app/dreambig_firebase_credentioal.json")
        firebase_admin.initialize_app(cred)
        print("✓ Firebase initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ Firebase initialization failed: {e}")
        return False

def list_firebase_users():
    """List all users in Firebase Authentication"""
    try:
        # List all users
        page = auth.list_users()
        users = []
        
        print(f"\n📋 Firebase Users ({len(page.users)} found):")
        print("-" * 60)
        
        for user in page.users:
            user_info = {
                'uid': user.uid,
                'email': user.email,
                'display_name': user.display_name,
                'email_verified': user.email_verified,
                'disabled': user.disabled,
                'creation_time': str(getattr(user.user_metadata, 'creation_time', None)) if user.user_metadata else None,
                'last_sign_in': str(getattr(user.user_metadata, 'last_sign_in_time', None)) if user.user_metadata else None
            }
            users.append(user_info)
            
            print(f"Email: {user.email}")
            print(f"UID: {user.uid}")
            print(f"Display Name: {user.display_name}")
            print(f"Email Verified: {user.email_verified}")
            print(f"Disabled: {user.disabled}")
            print(f"Created: {user_info['creation_time']}")
            print(f"Last Sign In: {user_info['last_sign_in']}")
            print("-" * 60)
        
        if not users:
            print("No users found in Firebase Authentication!")
            print("\n💡 To fix the login issue:")
            print("1. Register a new user first using the registration form")
            print("2. Or create a test user using the Firebase Console")
            
        return users
        
    except Exception as e:
        print(f"✗ Error listing users: {e}")
        return []

def test_firebase_config():
    """Test Firebase configuration"""
    try:
        with open("app/static/js/firebase-config.js", "r") as f:
            config_content = f.read()
        
        print("\n🔧 Frontend Firebase Configuration:")
        print("-" * 40)
        
        # Extract config values
        lines = config_content.split('\n')
        for line in lines:
            if any(key in line for key in ['apiKey', 'authDomain', 'projectId', 'storageBucket']):
                print(line.strip())
        
        # Check if project IDs match
        with open("app/dreambig_firebase_credentioal.json", "r") as f:
            backend_config = json.load(f)
        
        print(f"\n🔧 Backend Firebase Project ID: {backend_config['project_id']}")
        
        if 'dreambig-6d10e' in config_content and backend_config['project_id'] == 'dreambig-6d10e':
            print("✓ Project IDs match between frontend and backend")
        else:
            print("✗ Project ID mismatch detected!")
            
    except Exception as e:
        print(f"✗ Error checking config: {e}")

def create_test_user():
    """Create a test user for debugging"""
    try:
        test_email = "<EMAIL>"
        test_password = "testpassword123"
        
        print(f"\n🧪 Creating test user: {test_email}")
        
        user = auth.create_user(
            email=test_email,
            password=test_password,
            display_name="Test User",
            email_verified=True
        )
        
        print(f"✓ Test user created successfully!")
        print(f"Email: {test_email}")
        print(f"Password: {test_password}")
        print(f"UID: {user.uid}")
        
        return True
        
    except auth.EmailAlreadyExistsError:
        print(f"✓ Test user {test_email} already exists")
        print(f"You can try logging in with:")
        print(f"Email: {test_email}")
        print(f"Password: {test_password}")
        return True
        
    except Exception as e:
        print(f"✗ Error creating test user: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Firebase Debug Tool")
    print("=" * 50)
    
    # Initialize Firebase
    if not initialize_firebase():
        exit(1)
    
    # Test configuration
    test_firebase_config()
    
    # List existing users
    users = list_firebase_users()
    
    # If no users exist, offer to create a test user
    if not users:
        response = input("\n❓ Would you like to create a test user? (y/n): ").lower()
        if response == 'y':
            create_test_user()
    
    print("\n✅ Debug complete!")
