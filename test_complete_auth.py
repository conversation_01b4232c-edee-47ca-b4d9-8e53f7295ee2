#!/usr/bin/env python3
"""
Complete authentication test
"""

import requests
import json

def test_complete_auth_flow():
    """Test complete authentication flow"""
    
    print("🔐 Testing Complete Authentication Flow")
    print("=" * 50)
    
    # Test user data
    test_user = {
        "name": "Complete Test User",
        "email": "<EMAIL>",
        "phone": "9876543297",
        "password": "testpassword123",
        "role": "tenant"
    }
    
    print(f"👤 Test User: {test_user['email']}")
    print(f"📱 Phone: {test_user['phone']}")
    
    # Step 1: Registration
    print("\n📝 Step 1: Registration")
    try:
        reg_response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/register",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {reg_response.status_code}")
        
        if reg_response.status_code == 200:
            reg_data = reg_response.json()
            print("✅ Registration successful!")
            print(f"   User ID: {reg_data.get('user', {}).get('id')}")
            print(f"   Firebase UID: {reg_data.get('user', {}).get('firebase_uid')}")
            
        elif reg_response.status_code == 400:
            reg_error = reg_response.json()
            if "already exists" in reg_error.get('detail', ''):
                print("💡 User already exists - continuing with login test")
            else:
                print(f"❌ Registration failed: {reg_error.get('detail')}")
                return False
        else:
            print(f"❌ Registration failed: {reg_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return False
    
    # Step 2: Instructions for manual login test
    print("\n🔐 Step 2: Login Test Instructions")
    print("Since login requires a real Firebase ID token, please test manually:")
    print()
    print("1. 🚀 Start your server:")
    print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print()
    print("2. 🌐 Visit: http://127.0.0.1:8000/")
    print()
    print("3. 🔑 Click 'Login' and use these credentials:")
    print(f"   Email: {test_user['email']}")
    print(f"   Password: {test_user['password']}")
    print()
    print("4. 🔍 Check browser console for any errors")
    print("   - Press F12 to open developer tools")
    print("   - Look at Console tab for error messages")
    print("   - Look at Network tab to see API requests")
    print()
    print("5. ✅ Expected behavior:")
    print("   - Login modal should close")
    print("   - User menu should appear in top right")
    print("   - Welcome message should show")
    print("   - No error messages in console")
    
    return True

def check_server_status():
    """Check if server is running and healthy"""
    
    print("🏥 Checking Server Health")
    
    try:
        # Check main page
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code == 200:
            print("✅ Main page accessible")
        else:
            print(f"❌ Main page error: {response.status_code}")
            return False
        
        # Check API docs
        response = requests.get("http://127.0.0.1:8000/api/docs")
        if response.status_code == 200:
            print("✅ API docs accessible")
        else:
            print(f"❌ API docs error: {response.status_code}")
        
        # Check auth endpoints
        response = requests.post("http://127.0.0.1:8000/api/v1/auth/login", json={"id_token": "test"})
        if response.status_code == 401:  # Expected for invalid token
            print("✅ Auth endpoint responding")
        else:
            print(f"⚠️ Auth endpoint unexpected response: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Server not running!")
        print("   Start with: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        return False
    except Exception as e:
        print(f"❌ Server check error: {str(e)}")
        return False

def check_firebase_status():
    """Check Firebase configuration"""
    
    print("\n🔥 Checking Firebase Status")
    
    try:
        from app.core.firebase import initialize_firebase, auth
        
        # Initialize Firebase
        initialize_firebase()
        print("✅ Firebase initialized")
        
        # Test Firebase connection
        try:
            users = auth.list_users(max_results=1)
            print("✅ Firebase connection working")
            print(f"   Found {len(users.users)} user(s) in Firebase")
            return True
        except Exception as e:
            print(f"❌ Firebase connection error: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ Firebase initialization error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 DreamBig Complete Authentication Test")
    print("=" * 60)
    
    # Check server
    if not check_server_status():
        exit(1)
    
    # Check Firebase
    firebase_ok = check_firebase_status()
    
    # Test authentication flow
    auth_ok = test_complete_auth_flow()
    
    print("\n" + "=" * 60)
    if firebase_ok and auth_ok:
        print("🎉 All checks passed!")
        print("\n💡 Next steps:")
        print("   1. Test login in browser")
        print("   2. Check for any console errors")
        print("   3. Verify user authentication works")
    else:
        print("❌ Some checks failed")
        
    print("\n🔧 Troubleshooting tips:")
    print("   - Check server logs for detailed error messages")
    print("   - Verify Firebase credentials are correct")
    print("   - Ensure database is accessible")
    print("   - Check browser console for client-side errors")
