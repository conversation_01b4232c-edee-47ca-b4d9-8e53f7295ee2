<!DOCTYPE html>
<html>
<head>
    <title>Test Services API</title>
</head>
<body>
    <h1>Services API Test</h1>
    <button onclick="testAPI()">Test API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                console.log('Testing API call...');
                const response = await fetch('http://127.0.0.1:8000/api/v1/services/providers');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API data:', data);
                
                resultDiv.innerHTML = `
                    <h3>Success! Found ${data.length} services:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('API test error:', error);
                resultDiv.innerHTML = `<h3>Error: ${error.message}</h3>`;
            }
        }
    </script>
</body>
</html>
