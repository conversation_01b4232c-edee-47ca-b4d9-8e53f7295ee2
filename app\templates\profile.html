<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .profile-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }

        .profile-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: #667eea;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .preference-group {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .preference-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .checkbox-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .range-input {
            margin: 15px 0;
        }

        .range-values {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .notification-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            background: #e9ecef;
        }

        .notification-item.unread {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .notification-icon.welcome {
            background: #4caf50;
        }

        .notification-icon.property {
            background: #ff9800;
        }

        .notification-icon.kyc {
            background: #2196f3;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .notification-message {
            color: #666;
            font-size: 0.9rem;
        }

        .notification-time {
            font-size: 0.8rem;
            color: #999;
        }

        .mark-read-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .profile-container {
                padding: 15px;
            }

            .profile-tabs {
                flex-direction: column;
            }

            .tab-content {
                padding: 25px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="profile-container">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
            </div>
            <h1 id="profileHeaderName">Loading...</h1>
            <p id="profileHeaderEmail">Loading...</p>
            <div id="kycStatus" style="margin-top: 15px;">
                <span class="badge" id="kycBadge">Checking KYC Status...</span>
            </div>
        </div>

        <!-- Profile Tabs -->
        <div class="profile-tabs">
            <button class="tab-button active" onclick="switchTab('personal')">
                <i class="fas fa-user"></i>
                Personal Info
            </button>
            <button class="tab-button" onclick="switchTab('preferences')">
                <i class="fas fa-cog"></i>
                Preferences
            </button>
            <button class="tab-button" onclick="switchTab('notifications')">
                <i class="fas fa-bell"></i>
                Notifications
            </button>
            <button class="tab-button" onclick="switchTab('analytics')">
                <i class="fas fa-chart-bar"></i>
                Analytics
            </button>
        </div>

        <!-- Personal Information Tab -->
        <div class="tab-content active" id="personalTab">
            <form id="personalInfoForm">
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user-edit"></i>
                        Personal Information
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">Full Name</label>
                            <input type="text" id="name" name="name" required>
                            <span class="error-message" id="nameError"></span>
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" required>
                            <span class="error-message" id="phoneError"></span>
                        </div>
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" disabled>
                            <small>Email cannot be changed</small>
                        </div>
                        <div class="form-group">
                            <label for="role">Role</label>
                            <select id="role" name="role" disabled>
                                <option value="tenant">Tenant</option>
                                <option value="owner">Owner</option>
                                <option value="agent">Agent</option>
                            </select>
                            <small>Role cannot be changed</small>
                        </div>
                    </div>
                </div>

                <button type="submit" class="auth-button" id="updatePersonalButton">
                    <span class="button-text">Update Information</span>
                    <div class="loading-spinner" id="personalSpinner"></div>
                </button>
            </form>
        </div>

        <!-- Preferences Tab -->
        <div class="tab-content" id="preferencesTab">
            <form id="preferencesForm">
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        Property Preferences
                    </div>

                    <div class="preference-group">
                        <div class="preference-title">
                            <i class="fas fa-home"></i>
                            Property Types
                        </div>
                        <div class="checkbox-group" id="propertyTypes">
                            <label class="checkbox-item">
                                <input type="checkbox" name="property_types" value="apartment">
                                <span>Apartment</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="property_types" value="house">
                                <span>House</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="property_types" value="villa">
                                <span>Villa</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="property_types" value="commercial">
                                <span>Commercial</span>
                            </label>
                        </div>
                    </div>

                    <div class="preference-group">
                        <div class="preference-title">
                            <i class="fas fa-rupee-sign"></i>
                            Budget Range
                        </div>
                        <div class="range-input">
                            <label>Minimum Budget: ₹<span id="budgetMinValue">0</span></label>
                            <input type="range" id="budgetMin" name="budget_min" min="0" max="10000000" step="100000" value="0">
                        </div>
                        <div class="range-input">
                            <label>Maximum Budget: ₹<span id="budgetMaxValue">5000000</span></label>
                            <input type="range" id="budgetMax" name="budget_max" min="0" max="10000000" step="100000" value="5000000">
                        </div>
                    </div>

                    <div class="preference-group">
                        <div class="preference-title">
                            <i class="fas fa-bed"></i>
                            BHK Preference
                        </div>
                        <div class="checkbox-group" id="bhkPreference">
                            <label class="checkbox-item">
                                <input type="checkbox" name="bhk_preference" value="1">
                                <span>1 BHK</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="bhk_preference" value="2">
                                <span>2 BHK</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="bhk_preference" value="3">
                                <span>3 BHK</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="bhk_preference" value="4">
                                <span>4+ BHK</span>
                            </label>
                        </div>
                    </div>
                </div>

                <button type="submit" class="auth-button" id="updatePreferencesButton">
                    <span class="button-text">Update Preferences</span>
                    <div class="loading-spinner" id="preferencesSpinner"></div>
                </button>
            </form>
        </div>

        <!-- Notifications Tab -->
        <div class="tab-content" id="notificationsTab">
            <div class="section-title">
                <i class="fas fa-bell"></i>
                Recent Notifications
            </div>
            <div id="notificationsList">
                <div class="loading-message">Loading notifications...</div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analyticsTab">
            <div class="section-title">
                <i class="fas fa-chart-line"></i>
                Your Activity Analytics
            </div>
            <div class="stats-grid" id="analyticsStats">
                <div class="stat-card">
                    <div class="stat-value" id="totalFavorites">-</div>
                    <div class="stat-label">Favorite Properties</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalViewed">-</div>
                    <div class="stat-label">Properties Viewed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activityScore">-</div>
                    <div class="stat-label">Activity Score</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="userType">-</div>
                    <div class="stat-label">User Type</div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/profile.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = '/login';
                return;
            }

            initializeProfile();
        });

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');
            
            // Load tab-specific data
            if (tabName === 'notifications') {
                loadNotifications();
            } else if (tabName === 'analytics') {
                loadAnalytics();
            }
        }
    </script>
</body>
</html>
