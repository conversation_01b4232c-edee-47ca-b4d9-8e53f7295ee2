<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .logout-btn { background-color: #dc3545; }
        .logout-btn:hover { background-color: #c82333; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication Test Page</h1>
        
        <div id="authStatus" class="status info">
            Checking authentication status...
        </div>
        
        <div id="userInfo"></div>
        
        <div style="margin: 20px 0;">
            <button onclick="checkAuth()">🔍 Check Auth Status</button>
            <button onclick="refreshToken()">🔄 Refresh Token</button>
            <button onclick="testAPI()">🌐 Test API Call</button>
            <button onclick="logout()" class="logout-btn">🚪 Logout</button>
        </div>
        
        <div>
            <h3>Navigation Test:</h3>
            <button onclick="goTo('/dashboard')">📊 Dashboard</button>
            <button onclick="goTo('/properties')">🏠 Properties</button>
            <button onclick="goTo('/login')">🔑 Login</button>
        </div>
        
        <div id="logs">
            <h3>📋 Logs:</h3>
            <pre id="logOutput"></pre>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="/static/js/firebase-config.js"></script>
    <script src="/static/js/auth.js"></script>
    
    <script>
        let logOutput = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput += `[${timestamp}] ${message}\n`;
            document.getElementById('logOutput').textContent = logOutput;
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            log(`STATUS: ${message}`);
        }
        
        function checkAuth() {
            log('=== Checking Authentication ===');
            
            const isAuth = authManager.isAuthenticated();
            const currentUser = authManager.currentUser;
            const storedUser = localStorage.getItem('user');
            const storedToken = localStorage.getItem('access_token');
            
            log(`isAuthenticated(): ${isAuth}`);
            log(`currentUser: ${currentUser ? currentUser.email : 'null'}`);
            log(`localStorage user: ${storedUser ? 'exists' : 'null'}`);
            log(`localStorage token: ${storedToken ? 'exists' : 'null'}`);
            
            if (isAuth) {
                updateStatus('✅ User is authenticated', 'success');
                if (storedUser) {
                    const userData = JSON.parse(storedUser);
                    document.getElementById('userInfo').innerHTML = `
                        <h3>👤 User Info:</h3>
                        <p><strong>Email:</strong> ${userData.email}</p>
                        <p><strong>UID:</strong> ${userData.uid}</p>
                        <p><strong>Display Name:</strong> ${userData.displayName || 'Not set'}</p>
                    `;
                }
            } else {
                updateStatus('❌ User is not authenticated', 'error');
                document.getElementById('userInfo').innerHTML = '';
            }
        }
        
        async function refreshToken() {
            log('=== Refreshing Token ===');
            try {
                const token = await authManager.getCurrentUserToken();
                if (token) {
                    log('Token refreshed successfully');
                    updateStatus('✅ Token refreshed', 'success');
                } else {
                    log('Failed to get token');
                    updateStatus('❌ Failed to refresh token', 'error');
                }
            } catch (error) {
                log(`Token refresh error: ${error.message}`);
                updateStatus('❌ Token refresh failed', 'error');
            }
        }
        
        async function testAPI() {
            log('=== Testing API Call ===');
            try {
                const token = localStorage.getItem('access_token');
                if (!token) {
                    throw new Error('No access token found');
                }
                
                const response = await fetch('/api/v1/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`API Response Status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`API Response: ${JSON.stringify(data, null, 2)}`);
                    updateStatus('✅ API call successful', 'success');
                } else {
                    const errorData = await response.text();
                    log(`API Error: ${errorData}`);
                    updateStatus('❌ API call failed', 'error');
                }
            } catch (error) {
                log(`API Test Error: ${error.message}`);
                updateStatus('❌ API test failed', 'error');
            }
        }
        
        function logout() {
            log('=== Logging Out ===');
            authManager.logout();
        }
        
        function goTo(path) {
            log(`=== Navigating to ${path} ===`);
            window.location.href = path;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('=== Page Loaded ===');
            
            // Wait a bit for Firebase to initialize
            setTimeout(() => {
                checkAuth();
            }, 1000);
            
            // Set up periodic auth checks
            setInterval(() => {
                log('--- Periodic Auth Check ---');
                checkAuth();
            }, 10000); // Check every 10 seconds
        });
    </script>
</body>
</html>
