#!/usr/bin/env python3
"""
Test script to debug login flow
"""

import requests
import json

def test_login_endpoint():
    """Test the login endpoint with a mock token"""
    
    print("🧪 Testing Login Endpoint")
    
    # This is a mock token - in real scenario, you'd get this from Firebase client
    mock_token = "mock_firebase_token_for_testing"
    
    login_data = {
        "id_token": mock_token
    }
    
    print(f"📡 Sending login request with token: {mock_token[:20]}...")
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📡 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📡 Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📡 Response Text: {response.text}")
        
        if response.status_code == 401:
            print("✅ Expected 401 error for mock token - this means the endpoint is working")
            return True
        elif response.status_code == 200:
            print("✅ Login successful (unexpected with mock token)")
            return True
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure your FastAPI server is running on port 8000")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_registration_and_login():
    """Test full registration and login flow"""
    
    print("\n🔄 Testing Full Registration + Login Flow")
    
    # Step 1: Register a user
    test_user = {
        "name": "Login Test User",
        "email": "<EMAIL>",
        "phone": "9876543298",
        "password": "testpassword123",
        "role": "tenant"
    }
    
    print(f"📝 Step 1: Registering user {test_user['email']}")
    
    try:
        reg_response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/register",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"📡 Registration Status: {reg_response.status_code}")
        
        if reg_response.status_code == 200:
            print("✅ Registration successful")
            reg_data = reg_response.json()
            firebase_uid = reg_data.get('user', {}).get('firebase_uid')
            print(f"🔥 Firebase UID: {firebase_uid}")
            
        elif reg_response.status_code == 400:
            reg_error = reg_response.json()
            if "already exists" in reg_error.get('detail', ''):
                print("💡 User already exists - this is fine for testing")
            else:
                print(f"❌ Registration failed: {reg_error.get('detail')}")
                return False
        else:
            print(f"❌ Registration failed with status: {reg_response.status_code}")
            print(f"   Response: {reg_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return False
    
    print("\n💡 To test login, you need to:")
    print("   1. Use the web interface to login with Firebase")
    print("   2. The Firebase client will provide a valid ID token")
    print("   3. That token can then be used with the login endpoint")
    print("\n🌐 Visit: http://127.0.0.1:8000/")
    print("   - Click 'Login'")
    print("   - Use email: <EMAIL>")
    print("   - Use password: testpassword123")
    
    return True

def check_firebase_config():
    """Check if Firebase is configured properly"""
    
    print("\n🔥 Checking Firebase Configuration")
    
    try:
        from app.core.firebase import auth
        print("✅ Firebase Admin SDK imported successfully")
        
        # Try to list users (this will fail if Firebase is not configured)
        try:
            users = auth.list_users(max_results=1)
            print("✅ Firebase connection working")
            return True
        except Exception as e:
            print(f"❌ Firebase connection failed: {str(e)}")
            print("💡 Make sure your Firebase service account key is properly configured")
            return False
            
    except Exception as e:
        print(f"❌ Firebase import failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 DreamBig Login Flow Test")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/")
        if response.status_code != 200:
            print("❌ Server is not responding correctly")
            exit(1)
        print("✅ Server is running")
    except:
        print("❌ Server is not running!")
        print("   Please start the server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        exit(1)
    
    # Check Firebase configuration
    firebase_ok = check_firebase_config()
    
    # Test login endpoint
    login_ok = test_login_endpoint()
    
    # Test registration and provide login instructions
    reg_ok = test_registration_and_login()
    
    print("\n" + "=" * 50)
    if firebase_ok and login_ok and reg_ok:
        print("🎉 All tests completed!")
        print("\n💡 Next steps:")
        print("   1. Visit http://127.0.0.1:8000/")
        print("   2. Try logging in with the test user")
        print("   3. Check browser console for any errors")
    else:
        print("❌ Some tests failed. Check the errors above.")
