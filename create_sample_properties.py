#!/usr/bin/env python3
"""
Script to create sample properties with images and videos for testing
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models
from app.db.session import engine

# Create database tables if they don't exist
models.Base.metadata.create_all(bind=engine)

def check_existing_properties():
    """Check current properties in database"""
    db = next(get_db())
    
    properties = db.query(models.Property).all()
    print(f"🏠 Found {len(properties)} existing properties:")
    
    for prop in properties:
        print(f"  • {prop.title} - {prop.city} - ₹{prop.price:,.0f}")
        
        # Check images
        images = db.query(models.PropertyImage).filter(
            models.PropertyImage.property_id == prop.id
        ).all()
        print(f"    📸 Images: {len(images)}")
        
    db.close()
    return len(properties)

def get_sample_owner():
    """Get or create a sample owner for properties"""
    db = next(get_db())
    
    # Try to find an existing user
    owner = db.query(models.User).first()
    
    if not owner:
        # Create a sample owner
        owner_data = {
            "firebase_uid": "sample_owner_123",
            "email": "<EMAIL>",
            "name": "Property Owner",
            "phone": "+************",
            "role": models.UserRole.OWNER,
            "is_active": True,
            "kyc_verified": True
        }
        
        owner = models.User(**owner_data)
        db.add(owner)
        db.commit()
        db.refresh(owner)
        print(f"✅ Created sample owner: {owner.name}")
    else:
        print(f"✅ Using existing owner: {owner.name}")
    
    db.close()
    return owner.id

def create_sample_properties():
    """Create sample properties with rich media"""
    db = next(get_db())
    
    owner_id = get_sample_owner()
    
    sample_properties = [
        {
            "title": "Luxury 3BHK Apartment in Bandra",
            "description": "Stunning 3BHK apartment with sea view, modern amenities, and prime location in Bandra West. Features include modular kitchen, marble flooring, and 24/7 security.",
            "price": 8500000.0,
            "bhk": 3,
            "area": 1200.0,
            "property_type": models.PropertyType.APARTMENT,
            "furnishing": models.FurnishingType.FURNISHED,
            "address": "Carter Road, Bandra West",
            "city": "Mumbai",
            "state": "Maharashtra",
            "pincode": "400050",
            "latitude": 19.0596,
            "longitude": 72.8295,
            "status": models.PropertyStatus.AVAILABLE,
            "is_verified": True,
            "images": [
                "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800",
                "https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800",
                "https://images.unsplash.com/photo-1560185127-6ed189bf02f4?w=800",
                "https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800"
            ],
            "videos": [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
            ]
        },
        {
            "title": "Modern 2BHK Villa in Gurgaon",
            "description": "Contemporary villa with private garden, parking for 2 cars, and all modern amenities. Located in a gated community with club house and swimming pool.",
            "price": 12000000.0,
            "bhk": 2,
            "area": 1800.0,
            "property_type": models.PropertyType.VILLA,
            "furnishing": models.FurnishingType.SEMI_FURNISHED,
            "address": "Sector 47, Golf Course Road",
            "city": "Gurgaon",
            "state": "Haryana",
            "pincode": "122018",
            "latitude": 28.4595,
            "longitude": 77.0266,
            "status": models.PropertyStatus.AVAILABLE,
            "is_verified": True,
            "images": [
                "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800",
                "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800",
                "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?w=800",
                "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?w=800"
            ],
            "videos": [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
            ]
        },
        {
            "title": "Spacious 4BHK House in Koramangala",
            "description": "Independent house with terrace garden, servant quarters, and ample parking. Perfect for families looking for space and privacy in the heart of Bangalore.",
            "price": 15000000.0,
            "bhk": 4,
            "area": 2500.0,
            "property_type": models.PropertyType.HOUSE,
            "furnishing": models.FurnishingType.UNFURNISHED,
            "address": "5th Block, Koramangala",
            "city": "Bangalore",
            "state": "Karnataka",
            "pincode": "560095",
            "latitude": 12.9352,
            "longitude": 77.6245,
            "status": models.PropertyStatus.AVAILABLE,
            "is_verified": True,
            "images": [
                "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800",
                "https://images.unsplash.com/photo-1605276374104-dee2a0ed3cd6?w=800",
                "https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800",
                "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800"
            ],
            "videos": [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_5mb.mp4"
            ]
        },
        {
            "title": "Commercial Office Space in Cyber City",
            "description": "Premium office space with modern infrastructure, high-speed elevators, and 24/7 power backup. Ideal for IT companies and startups.",
            "price": 25000000.0,
            "bhk": 0,  # Not applicable for commercial
            "area": 3000.0,
            "property_type": models.PropertyType.COMMERCIAL,
            "furnishing": models.FurnishingType.UNFURNISHED,
            "address": "DLF Cyber City, Phase 2",
            "city": "Gurgaon",
            "state": "Haryana",
            "pincode": "122002",
            "latitude": 28.4949,
            "longitude": 77.0869,
            "status": models.PropertyStatus.AVAILABLE,
            "is_verified": True,
            "images": [
                "https://images.unsplash.com/photo-1497366216548-37526070297c?w=800",
                "https://images.unsplash.com/photo-1497366811353-6870744d04b2?w=800",
                "https://images.unsplash.com/photo-1604328698692-f76ea9498e76?w=800",
                "https://images.unsplash.com/photo-1541746972996-4e0b0f93e586?w=800"
            ],
            "videos": [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
            ]
        },
        {
            "title": "Affordable 1BHK Apartment in Andheri",
            "description": "Compact yet comfortable 1BHK apartment perfect for young professionals. Close to metro station and IT parks. Ready to move in.",
            "price": 4500000.0,
            "bhk": 1,
            "area": 650.0,
            "property_type": models.PropertyType.APARTMENT,
            "furnishing": models.FurnishingType.FURNISHED,
            "address": "Andheri East, Near Metro Station",
            "city": "Mumbai",
            "state": "Maharashtra",
            "pincode": "400069",
            "latitude": 19.1136,
            "longitude": 72.8697,
            "status": models.PropertyStatus.AVAILABLE,
            "is_verified": True,
            "images": [
                "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800",
                "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800",
                "https://images.unsplash.com/photo-1560448075-bb485b067938?w=800"
            ],
            "videos": [
                "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4"
            ]
        }
    ]
    
    print(f"\n🏗️  Creating {len(sample_properties)} sample properties...")
    
    created_count = 0
    for prop_data in sample_properties:
        # Extract images and videos
        images = prop_data.pop('images', [])
        videos = prop_data.pop('videos', [])
        
        # Check if property already exists
        existing = db.query(models.Property).filter(
            models.Property.title == prop_data["title"]
        ).first()
        
        if existing:
            print(f"  ⏭️  Skipped: {prop_data['title']} (already exists)")
            continue
        
        # Create property
        prop_data['owner_id'] = owner_id
        property_obj = models.Property(**prop_data)
        db.add(property_obj)
        db.flush()  # Get the ID without committing
        
        # Add images
        for image_url in images:
            image_obj = models.PropertyImage(
                url=image_url,
                property_id=property_obj.id
            )
            db.add(image_obj)

        # Add videos (if PropertyVideo model exists)
        try:
            for video_url in videos:
                video_obj = models.PropertyVideo(
                    url=video_url,
                    property_id=property_obj.id,
                    title=f"Property Tour - {prop_data['title']}",
                    description="Virtual tour of the property"
                )
                db.add(video_obj)
        except AttributeError:
            # PropertyVideo model doesn't exist yet
            pass
        
        # Add property features
        features = [
            {"name": "Parking", "value": "Available"},
            {"name": "Security", "value": "24/7"},
            {"name": "Power Backup", "value": "Yes"},
            {"name": "Water Supply", "value": "24/7"},
            {"name": "Gym", "value": "Available"},
            {"name": "Swimming Pool", "value": "Available"}
        ]
        
        for feature in features[:3]:  # Add first 3 features
            feature_obj = models.PropertyFeature(
                name=feature["name"],
                value=feature["value"],
                property_id=property_obj.id
            )
            db.add(feature_obj)
        
        print(f"  ✅ Created: {prop_data['title']} with {len(images)} images")
        created_count += 1
    
    try:
        db.commit()
        print(f"\n✅ Successfully created {created_count} properties!")
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating properties: {e}")
    finally:
        db.close()

def test_properties_api():
    """Test the properties API"""
    import requests
    
    print("\n🧪 Testing Properties API...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/properties/", timeout=5)
        if response.status_code == 200:
            properties = response.json()
            print(f"  ✅ API working: Found {len(properties)} properties")
            
            for prop in properties[:3]:  # Show first 3
                print(f"    • {prop['title']} - ₹{prop['price']:,.0f}")
        else:
            print(f"  ❌ API error: HTTP {response.status_code}")
    except Exception as e:
        print(f"  ❌ API test failed: {e}")

if __name__ == "__main__":
    print("🏠 Property Sample Data Creator")
    print("=" * 50)
    
    # Check existing properties
    existing_count = check_existing_properties()
    
    if existing_count == 0:
        print("\n📝 No properties found. Creating sample properties...")
        create_sample_properties()
    else:
        response = input(f"\n❓ Found {existing_count} properties. Create more samples? (y/n): ").lower()
        if response == 'y':
            create_sample_properties()
    
    # Final check
    print("\n" + "="*50)
    print("📊 Final property count:")
    check_existing_properties()
    
    # Test API
    test_properties_api()
    
    print("\n✅ Property setup complete!")
    print("🌐 You can now test properties at:")
    print("   • http://localhost:8000/properties")
    print("   • http://localhost:8000/api/v1/properties/")
    print("   • http://localhost:8000/api/docs")
