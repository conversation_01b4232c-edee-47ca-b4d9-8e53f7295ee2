[alembic]
script_location = alembic
sqlalchemy.url = sqlite:///./sql_app.db

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handler = console
qualname =

[logger_sqlalchemy]
level = WARN
handler = console
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handler = console
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S