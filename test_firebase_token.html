<!DOCTYPE html>
<html>
<head>
    <title>Firebase Token Testing</title>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        textarea { width: 100%; height: 100px; margin: 10px 0; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Token Testing Tool</h1>
        
        <div class="section">
            <h2>Step 1: Configure Firebase</h2>
            <p>Replace the Firebase config below with your project settings:</p>
            <textarea id="firebase-config" placeholder="Paste your Firebase config here...">
{
  "apiKey": "your-api-key",
  "authDomain": "your-project.firebaseapp.com",
  "projectId": "your-project-id",
  "storageBucket": "your-project.appspot.com",
  "messagingSenderId": "123456789",
  "appId": "your-app-id"
}
            </textarea>
            <button onclick="initializeFirebase()">Initialize Firebase</button>
            <div id="firebase-status"></div>
        </div>

        <div class="section">
            <h2>Step 2: Use Custom Token</h2>
            <p>Paste the custom token from the Python script:</p>
            <textarea id="custom-token" placeholder="Paste custom token here..."></textarea>
            <button onclick="signInWithCustomToken()">Sign In with Custom Token</button>
            <div id="signin-status"></div>
        </div>

        <div class="section">
            <h2>Step 3: Get ID Token</h2>
            <button onclick="getIdToken()">Get ID Token</button>
            <div id="user-info"></div>
            <textarea id="id-token" placeholder="ID Token will appear here..." readonly></textarea>
        </div>

        <div class="section">
            <h2>Step 4: Test API</h2>
            <button onclick="testAPI()">Test API with ID Token</button>
            <div id="api-result"></div>
        </div>

        <div class="section">
            <h2>Alternative: Email/Password Sign In</h2>
            <input type="email" id="email" placeholder="Email" style="width: 200px;">
            <input type="password" id="password" placeholder="Password" style="width: 200px;">
            <button onclick="signInWithEmail()">Sign In</button>
            <div id="email-signin-status"></div>
        </div>
    </div>

    <script>
        let auth = null;
        let currentUser = null;

        function initializeFirebase() {
            try {
                const configText = document.getElementById('firebase-config').value;
                const firebaseConfig = JSON.parse(configText);
                
                firebase.initializeApp(firebaseConfig);
                auth = firebase.auth();
                
                // Auth state observer
                auth.onAuthStateChanged((user) => {
                    currentUser = user;
                    if (user) {
                        document.getElementById('user-info').innerHTML = 
                            `<div class="success">✅ Signed in as: ${user.email} (UID: ${user.uid})</div>`;
                    } else {
                        document.getElementById('user-info').innerHTML = 
                            `<div class="info">Not signed in</div>`;
                    }
                });
                
                document.getElementById('firebase-status').innerHTML = 
                    '<div class="success">✅ Firebase initialized successfully!</div>';
            } catch (error) {
                document.getElementById('firebase-status').innerHTML = 
                    `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        function signInWithCustomToken() {
            if (!auth) {
                alert('Please initialize Firebase first!');
                return;
            }

            const customToken = document.getElementById('custom-token').value.trim();
            if (!customToken) {
                alert('Please enter a custom token!');
                return;
            }

            auth.signInWithCustomToken(customToken)
                .then((userCredential) => {
                    document.getElementById('signin-status').innerHTML = 
                        '<div class="success">✅ Signed in with custom token successfully!</div>';
                    console.log('User signed in:', userCredential.user);
                })
                .catch((error) => {
                    document.getElementById('signin-status').innerHTML = 
                        `<div class="error">❌ Error: ${error.message}</div>`;
                    console.error('Sign in error:', error);
                });
        }

        function signInWithEmail() {
            if (!auth) {
                alert('Please initialize Firebase first!');
                return;
            }

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                alert('Please enter email and password!');
                return;
            }

            auth.signInWithEmailAndPassword(email, password)
                .then((userCredential) => {
                    document.getElementById('email-signin-status').innerHTML = 
                        '<div class="success">✅ Signed in with email successfully!</div>';
                })
                .catch((error) => {
                    document.getElementById('email-signin-status').innerHTML = 
                        `<div class="error">❌ Error: ${error.message}</div>`;
                });
        }

        function getIdToken() {
            if (!currentUser) {
                alert('Please sign in first!');
                return;
            }

            currentUser.getIdToken(true)
                .then((idToken) => {
                    document.getElementById('id-token').value = idToken;
                    console.log('ID Token:', idToken);
                    
                    // Show token info
                    const tokenParts = idToken.split('.');
                    const payload = JSON.parse(atob(tokenParts[1]));
                    
                    document.getElementById('user-info').innerHTML += 
                        `<div class="info">🎫 ID Token generated! Expires: ${new Date(payload.exp * 1000).toLocaleString()}</div>`;
                })
                .catch((error) => {
                    console.error('Error getting ID token:', error);
                    alert('Error getting ID token: ' + error.message);
                });
        }

        function testAPI() {
            const idToken = document.getElementById('id-token').value;
            if (!idToken) {
                alert('Please get an ID token first!');
                return;
            }

            // Test the login endpoint
            fetch('http://127.0.0.1:8000/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${idToken}`
                },
                body: JSON.stringify({
                    id_token: idToken
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-result').innerHTML = 
                    `<div class="success">✅ API Test Successful!</div>
                     <pre>${JSON.stringify(data, null, 2)}</pre>`;
                console.log('API Response:', data);
            })
            .catch(error => {
                document.getElementById('api-result').innerHTML = 
                    `<div class="error">❌ API Test Failed: ${error.message}</div>`;
                console.error('API Error:', error);
            });
        }
    </script>
</body>
</html>
