#!/usr/bin/env python3
"""
Test script to verify registration functionality
"""

import requests
import json

def test_registration():
    """Test the registration endpoint"""
    
    # Test data
    test_user = {
        "name": "Test User",
        "email": "<EMAIL>",
        "phone": "9876543210",
        "password": "testpassword123",
        "role": "tenant"
    }
    
    print("🧪 Testing Registration Endpoint")
    print(f"📧 Email: {test_user['email']}")
    print(f"👤 Name: {test_user['name']}")
    print(f"📱 Phone: {test_user['phone']}")
    print(f"🎭 Role: {test_user['role']}")
    
    try:
        # Make registration request
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/register",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Registration Successful!")
            print(f"👤 User ID: {data.get('user', {}).get('id')}")
            print(f"📧 Email: {data.get('user', {}).get('email')}")
            print(f"🔥 Firebase UID: {data.get('user', {}).get('firebase_uid')}")
            return True
            
        elif response.status_code == 400:
            error_data = response.json()
            print(f"❌ Registration Failed: {error_data.get('detail')}")
            
            if "already exists" in error_data.get('detail', ''):
                print("💡 User already exists - this is expected if you've run this test before")
                return test_login(test_user['email'])
            
        else:
            print(f"❌ Unexpected Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure your FastAPI server is running on port 8000")
        print("   Run: python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        
    return False

def test_login(email):
    """Test login with existing user"""
    print(f"\n🔐 Testing Login for existing user: {email}")
    
    # For login, we need a Firebase ID token
    # This is just a placeholder - in real scenario, you'd get this from Firebase client
    print("💡 Login requires Firebase ID token from client-side authentication")
    print("   This test only verifies registration. Use the web interface to test login.")
    
    return True

def check_server():
    """Check if the server is running"""
    try:
        response = requests.get("http://127.0.0.1:8000/")
        return response.status_code == 200
    except:
        return False

if __name__ == "__main__":
    print("🚀 DreamBig Registration Test")
    print("=" * 50)
    
    # Check if server is running
    if not check_server():
        print("❌ Server is not running!")
        print("   Please start the server with:")
        print("   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        exit(1)
    
    print("✅ Server is running")
    
    # Test registration
    success = test_registration()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("💡 You can now test the full flow in your browser:")
        print("   http://127.0.0.1:8000/")
    else:
        print("\n❌ Test failed. Check the error messages above.")
