<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Details - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .property-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .property-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .property-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .property-location {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .property-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 20px;
        }

        .property-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-favorite {
            background: #ff4757;
            color: white;
        }

        .btn-favorite.active {
            background: #ff3742;
        }

        .property-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .property-main {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .property-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .sidebar-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .property-image-gallery {
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            margin-bottom: 30px;
        }

        .property-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .detail-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .detail-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .detail-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .property-description {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: #667eea;
        }

        .owner-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .owner-avatar {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .owner-details h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .owner-details p {
            color: #666;
            font-size: 0.9rem;
        }

        .contact-btn {
            width: 100%;
            margin-top: 15px;
        }

        .similar-properties {
            margin-top: 40px;
        }

        .similar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .similar-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .similar-card:hover {
            transform: translateY(-5px);
        }

        .similar-image {
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .similar-content {
            padding: 20px;
        }

        .similar-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .similar-price {
            color: #667eea;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .loading-message {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .property-container {
                padding: 15px;
            }

            .property-content {
                grid-template-columns: 1fr;
            }

            .property-actions {
                justify-content: center;
            }

            .property-details-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .similar-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="property-container">
        <!-- Property Header -->
        <div class="property-header" id="propertyHeader">
            <div class="loading-message">Loading property details...</div>
        </div>

        <!-- Property Content -->
        <div class="property-content" id="propertyContent" style="display: none;">
            <!-- Main Content -->
            <div class="property-main">
                <!-- Image Gallery -->
                <div class="property-image-gallery">
                    <i class="fas fa-home"></i>
                </div>

                <!-- Property Details -->
                <div class="property-details-grid" id="propertyDetailsGrid">
                    <!-- Details will be populated by JavaScript -->
                </div>

                <!-- Description -->
                <div class="property-description">
                    <div class="section-title">
                        <i class="fas fa-info-circle"></i>
                        Description
                    </div>
                    <p id="propertyDescription">Loading description...</p>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="property-sidebar">
                <!-- Owner Information -->
                <div class="sidebar-card">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        Owner Details
                    </div>
                    <div class="owner-info" id="ownerInfo">
                        <div class="loading-message">Loading owner info...</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="sidebar-card">
                    <div class="section-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </div>
                    <button class="action-btn btn-primary contact-btn" onclick="contactOwner()">
                        <i class="fas fa-phone"></i>
                        Contact Owner
                    </button>
                    <button class="action-btn btn-secondary contact-btn" onclick="scheduleVisit()">
                        <i class="fas fa-calendar"></i>
                        Schedule Visit
                    </button>
                    <button class="action-btn btn-secondary contact-btn" onclick="shareProperty()">
                        <i class="fas fa-share"></i>
                        Share Property
                    </button>
                </div>
            </div>
        </div>

        <!-- Similar Properties -->
        <div class="similar-properties" id="similarProperties" style="display: none;">
            <div class="section-title">
                <i class="fas fa-home"></i>
                Similar Properties
            </div>
            <div class="similar-grid" id="similarGrid">
                <!-- Similar properties will be populated by JavaScript -->
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/property-details.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get property ID from URL
            const pathParts = window.location.pathname.split('/');
            const propertyId = pathParts[pathParts.length - 1];
            
            if (propertyId && !isNaN(propertyId)) {
                initializePropertyDetails(parseInt(propertyId));
            } else {
                // Redirect to properties page if no valid ID
                window.location.href = '/properties';
            }
        });
    </script>
</body>
</html>
