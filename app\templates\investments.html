<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investments - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .investments-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .investments-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }

        .portfolio-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .summary-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 20px;
        }

        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .summary-label {
            color: #666;
            font-size: 0.9rem;
        }

        .investments-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .tab-button {
            flex: 1;
            padding: 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .investments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .investment-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .investment-card:hover {
            transform: translateY(-5px);
        }

        .investment-image {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
        }

        .investment-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .investment-content {
            padding: 20px;
        }

        .investment-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .investment-location {
            color: #666;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .investment-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .detail-label {
            font-size: 0.8rem;
            color: #666;
        }

        .investment-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .add-investment-fab {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .add-investment-fab:hover {
            transform: scale(1.1);
        }

        .loading-message {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-message {
            text-align: center;
            padding: 60px;
            color: #666;
        }

        @media (max-width: 768px) {
            .investments-container {
                padding: 15px;
            }

            .investments-tabs {
                flex-direction: column;
            }

            .investments-grid {
                grid-template-columns: 1fr;
            }

            .portfolio-summary {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="investments-container">
        <!-- Investments Header -->
        <div class="investments-header">
            <h1>Investment Portfolio</h1>
            <p>Track your real estate investments and discover new opportunities</p>
        </div>

        <!-- Portfolio Summary -->
        <div class="portfolio-summary" id="portfolioSummary">
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="summary-value" id="totalInvestments">-</div>
                <div class="summary-label">Total Investments</div>
            </div>
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="summary-value" id="totalValue">-</div>
                <div class="summary-label">Portfolio Value</div>
            </div>
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="summary-value" id="avgROI">-</div>
                <div class="summary-label">Average ROI</div>
            </div>
            <div class="summary-card">
                <div class="summary-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="summary-value" id="monthlyReturns">-</div>
                <div class="summary-label">Monthly Returns</div>
            </div>
        </div>

        <!-- Investment Tabs -->
        <div class="investments-tabs">
            <button class="tab-button active" onclick="switchTab('my-investments')">
                <i class="fas fa-briefcase"></i>
                My Investments
            </button>
            <button class="tab-button" onclick="switchTab('opportunities')">
                <i class="fas fa-search"></i>
                Opportunities
            </button>
            <button class="tab-button" onclick="switchTab('analytics')">
                <i class="fas fa-chart-bar"></i>
                Analytics
            </button>
        </div>

        <!-- My Investments Tab -->
        <div class="tab-content active" id="my-investmentsTab">
            <div class="investments-grid" id="myInvestmentsGrid">
                <div class="loading-message">Loading your investments...</div>
            </div>
        </div>

        <!-- Investment Opportunities Tab -->
        <div class="tab-content" id="opportunitiesTab">
            <div class="investments-grid" id="opportunitiesGrid">
                <div class="loading-message">Loading investment opportunities...</div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analyticsTab">
            <div class="loading-message">Loading analytics...</div>
        </div>

        <!-- Add Investment FAB -->
        <button class="add-investment-fab" onclick="addInvestment()" title="Add New Investment">
            <i class="fas fa-plus"></i>
        </button>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script src="{{ url_for('static', path='/js/investments.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                authManager.showMessage('Please log in to view investments', 'error');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
                return;
            }

            initializeInvestments();
        });

        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tabs and contents
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            // Add active class to selected tab and content
            event.target.classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');
            
            // Load tab-specific data
            if (tabName === 'opportunities') {
                loadInvestmentOpportunities();
            } else if (tabName === 'analytics') {
                loadInvestmentAnalytics();
            }
        }

        function addInvestment() {
            window.location.href = '/add-investment';
        }
    </script>
</body>
</html>
