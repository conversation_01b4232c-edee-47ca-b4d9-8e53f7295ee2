<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .dashboard-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .user-info {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .user-info h2 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .user-detail {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
        }

        .user-detail i {
            color: #667eea;
            width: 20px;
        }

        .logout-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .logout-button {
            background: #ff4757;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .logout-button:hover {
            background: #ff3742;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);
        }

        @media (max-width: 768px) {
            .dashboard-header {
                padding: 30px 20px;
            }

            .dashboard-header h1 {
                font-size: 2rem;
            }

            .dashboard-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="dashboard-container">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1>Welcome to Your Dashboard</h1>
            <p id="welcomeMessage">Manage your properties, investments, and more</p>
        </div>

        <!-- User Information -->
        <div class="user-info">
            <h2>
                <i class="fas fa-user-circle"></i>
                Your Account Information
            </h2>
            <div class="user-details" id="userDetails">
                <div class="user-detail">
                    <i class="fas fa-envelope"></i>
                    <span id="userEmail">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-user"></i>
                    <span id="userName">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-calendar"></i>
                    <span id="userJoined">Loading...</span>
                </div>
                <div class="user-detail">
                    <i class="fas fa-shield-alt"></i>
                    <span id="userStatus">Loading...</span>
                </div>
            </div>
        </div>

        <!-- AI Recommendations Section -->
        <div class="user-info" id="recommendationsSection" style="display: none;">
            <h2>
                <i class="fas fa-robot"></i>
                AI-Powered Recommendations
            </h2>
            <div id="recommendationsList">
                <div class="loading-message">Loading personalized recommendations...</div>
            </div>
        </div>

        <!-- Dashboard Cards -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div class="card-title">My Properties</div>
                <div class="card-description">
                    View and manage your property listings, track inquiries, and update property details.
                </div>
                <a href="/properties" class="card-button">View Properties</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="card-title">My Investments</div>
                <div class="card-description">
                    Track your real estate investments, view returns, and explore new opportunities.
                </div>
                <a href="/investments" class="card-button">View Investments</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="card-title">Services</div>
                <div class="card-description">
                    Access professional services like legal assistance, property management, and more.
                </div>
                <a href="/services" class="card-button">Browse Services</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="card-title">Favorites</div>
                <div class="card-description">
                    Quick access to your favorite properties and saved searches.
                </div>
                <button class="card-button" onclick="loadFavorites()">View Favorites</button>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="card-title">KYC Verification</div>
                <div class="card-description" id="kycCardDescription">
                    Complete your identity verification to access all features.
                </div>
                <a href="/kyc-verification" class="card-button" id="kycCardButton">Complete KYC</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="card-title">Profile Settings</div>
                <div class="card-description">
                    Update your profile, preferences, and notification settings.
                </div>
                <a href="/profile" class="card-button">Manage Profile</a>
            </div>
        </div>

        <!-- Logout Section -->
        <div class="logout-section">
            <h3>Session Management</h3>
            <p>Click below to securely log out of your account</p>
            <button class="logout-button" id="dashboardLogout">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </button>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is authenticated
            if (!authManager.isAuthenticated()) {
                window.location.href = '/login';
                return;
            }

            // Load user information
            loadUserInfo();

            // Setup logout button
            const logoutButton = document.getElementById('dashboardLogout');
            if (logoutButton) {
                logoutButton.addEventListener('click', async () => {
                    await authManager.logout();
                });
            }
        });

        async function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('user') || '{}');

            // Update welcome message
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage && user.displayName) {
                welcomeMessage.textContent = `Welcome back, ${user.displayName}!`;
            }

            // Load user profile from API
            try {
                const token = await authManager.getCurrentUserToken();
                if (token) {
                    const response = await fetch('/api/v1/users/me', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        const userData = await response.json();

                        // Update user details
                        const userEmail = document.getElementById('userEmail');
                        const userName = document.getElementById('userName');
                        const userJoined = document.getElementById('userJoined');
                        const userStatus = document.getElementById('userStatus');

                        if (userEmail) userEmail.textContent = userData.email || 'Not available';
                        if (userName) userName.textContent = userData.name || 'Not available';
                        if (userJoined) userJoined.textContent = new Date(userData.created_at).toLocaleDateString();
                        if (userStatus) userStatus.textContent = userData.kyc_verified ? 'KYC Verified' : 'KYC Pending';

                        // Update KYC card
                        updateKYCCard(userData.kyc_verified);

                        // Load recommendations
                        loadRecommendations();
                    }
                }
            } catch (error) {
                console.error('Error loading user info:', error);
                // Fallback to localStorage data
                const userEmail = document.getElementById('userEmail');
                const userName = document.getElementById('userName');
                const userJoined = document.getElementById('userJoined');
                const userStatus = document.getElementById('userStatus');

                if (userEmail) userEmail.textContent = user.email || 'Not available';
                if (userName) userName.textContent = user.displayName || 'Not available';
                if (userJoined) userJoined.textContent = 'Member since registration';
                if (userStatus) userStatus.textContent = 'Active Account';
            }
        }

        function updateKYCCard(isKYCVerified) {
            const kycCardDescription = document.getElementById('kycCardDescription');
            const kycCardButton = document.getElementById('kycCardButton');

            if (isKYCVerified) {
                if (kycCardDescription) {
                    kycCardDescription.textContent = 'Your identity has been verified successfully.';
                }
                if (kycCardButton) {
                    kycCardButton.textContent = 'View KYC Status';
                    kycCardButton.style.background = '#28a745';
                }
            }
        }

        async function loadRecommendations() {
            try {
                const token = await authManager.getCurrentUserToken();
                if (!token) return;

                const response = await fetch('/api/v1/users/recommendations?limit=5', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const recommendations = await response.json();
                    displayRecommendations(recommendations);
                }
            } catch (error) {
                console.error('Error loading recommendations:', error);
            }
        }

        function displayRecommendations(recommendations) {
            const recommendationsSection = document.getElementById('recommendationsSection');
            const recommendationsList = document.getElementById('recommendationsList');

            if (!recommendations || recommendations.length === 0) {
                return; // Don't show section if no recommendations
            }

            recommendationsSection.style.display = 'block';

            const recHTML = recommendations.map(rec => `
                <div style="display: flex; align-items: center; gap: 15px; padding: 15px;
                           background: #f8f9fa; border-radius: 10px; margin-bottom: 15px;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                               border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-home"></i>
                    </div>
                    <div style="flex: 1;">
                        <h4 style="margin-bottom: 5px; color: #333;">${rec.title || 'Property Recommendation'}</h4>
                        <p style="margin-bottom: 5px; color: #666; font-size: 0.9rem;">
                            ${rec.description || rec.location || 'Personalized recommendation based on your preferences'}
                        </p>
                        ${rec.price ? `<span style="color: #667eea; font-weight: 600;">₹${rec.price}</span>` : ''}
                    </div>
                    <button style="background: #667eea; color: white; border: none; padding: 8px 16px;
                                   border-radius: 5px; cursor: pointer;" onclick="viewProperty(${rec.id || 0})">
                        View Details
                    </button>
                </div>
            `).join('');

            recommendationsList.innerHTML = recHTML;
        }

        async function loadFavorites() {
            try {
                const token = await authManager.getCurrentUserToken();
                if (!token) {
                    authManager.showMessage('Please log in to view favorites', 'error');
                    return;
                }

                const response = await fetch('/api/v1/users/favorites', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const favorites = await response.json();
                    authManager.showMessage(`You have ${favorites.length} favorite properties`, 'info');
                    // Here you could redirect to a favorites page or show them in a modal
                } else {
                    authManager.showMessage('Failed to load favorites', 'error');
                }
            } catch (error) {
                console.error('Error loading favorites:', error);
                authManager.showMessage('Failed to load favorites', 'error');
            }
        }

        function viewProperty(propertyId) {
            if (propertyId) {
                window.location.href = `/properties/${propertyId}`;
            } else {
                window.location.href = '/properties';
            }
        }
    </script>
</body>
</html>
