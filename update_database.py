#!/usr/bin/env python3
"""
Script to update database schema with new PropertyVideo table
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from app.db.session import engine
from app.db import models

def update_database():
    """Create new database tables"""
    print("🔄 Updating database schema...")
    
    try:
        # Create all tables (will only create new ones)
        models.Base.metadata.create_all(bind=engine)
        print("✅ Database schema updated successfully!")
        
        # Check if PropertyVideo table was created
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if 'property_videos' in tables:
            print("✅ PropertyVideo table created successfully!")
        else:
            print("⚠️  PropertyVideo table not found")
            
        print(f"📊 Total tables in database: {len(tables)}")
        for table in sorted(tables):
            print(f"  • {table}")
            
    except Exception as e:
        print(f"❌ Error updating database: {e}")

if __name__ == "__main__":
    print("🗄️  Database Schema Updater")
    print("=" * 40)
    update_database()
