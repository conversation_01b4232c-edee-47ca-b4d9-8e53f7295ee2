<!DOCTYPE html>
<html>
<head>
    <title>Firebase Authentication Example</title>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
</head>
<body>
    <h1>Firebase Authentication</h1>
    
    <div id="login-section">
        <h2>Login</h2>
        <input type="email" id="email" placeholder="Email">
        <input type="password" id="password" placeholder="Password">
        <button onclick="signIn()">Sign In</button>
        <button onclick="signUp()">Sign Up</button>
    </div>
    
    <div id="user-section" style="display:none;">
        <h2>User Info</h2>
        <p id="user-info"></p>
        <p><strong>ID Token:</strong></p>
        <textarea id="token-display" rows="10" cols="80" readonly></textarea>
        <br><br>
        <button onclick="refreshToken()">Refresh Token</button>
        <button onclick="signOut()">Sign Out</button>
        <button onclick="testAPI()">Test API with Token</button>
    </div>

    <script>
        // Firebase configuration (replace with your config)
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

        // Auth state observer
        auth.onAuthStateChanged((user) => {
            if (user) {
                document.getElementById('login-section').style.display = 'none';
                document.getElementById('user-section').style.display = 'block';
                document.getElementById('user-info').textContent = 
                    `Logged in as: ${user.email} (UID: ${user.uid})`;
                
                // Get ID token
                user.getIdToken().then((token) => {
                    document.getElementById('token-display').value = token;
                    console.log('Firebase ID Token:', token);
                });
            } else {
                document.getElementById('login-section').style.display = 'block';
                document.getElementById('user-section').style.display = 'none';
            }
        });

        // Sign up function
        function signUp() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            auth.createUserWithEmailAndPassword(email, password)
                .then((userCredential) => {
                    console.log('User created:', userCredential.user);
                })
                .catch((error) => {
                    alert('Error: ' + error.message);
                });
        }

        // Sign in function
        function signIn() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            auth.signInWithEmailAndPassword(email, password)
                .then((userCredential) => {
                    console.log('User signed in:', userCredential.user);
                })
                .catch((error) => {
                    alert('Error: ' + error.message);
                });
        }

        // Sign out function
        function signOut() {
            auth.signOut();
        }

        // Refresh token function
        function refreshToken() {
            const user = auth.currentUser;
            if (user) {
                user.getIdToken(true).then((token) => {
                    document.getElementById('token-display').value = token;
                    console.log('Refreshed token:', token);
                });
            }
        }

        // Test API with token
        function testAPI() {
            const token = document.getElementById('token-display').value;
            
            fetch('http://127.0.0.1:8000/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    id_token: token
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('API Response:', data);
                alert('API Test: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                console.error('API Error:', error);
                alert('API Error: ' + error.message);
            });
        }
    </script>
</body>
</html>
