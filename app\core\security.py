from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.firebase import verify_token
from app.db.crud import get_user_by_firebase_uid
from app.db.session import get_db
from sqlalchemy.orm import Session

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    try:
        decode_token = await verify_token(credentials.credentials)
        firebase_uid = decode_token['uid']
        user = get_user_by_firebase_uid(db, firebase_uid)
        if not user:
            raise HTTPException(
                status_code = status.HTTP_401_UNAUTHORIZED,
                detail="User Not Found in data base"
            )
        return user
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code = status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        
def get_current_active_user(current_user: dict = Depends(get_current_user)):
    if not current_user.is_active: # type: ignore
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

def get_current_admin_user(current_user: dict = Depends(get_current_active_user)):
    if current_user.role != "admin": # type: ignore
        raise HTTPException(status_code= status.HTTP_403_FORBIDDEN, detail="Admin privileges requires")
    return current_user
