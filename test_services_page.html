<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        .service-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Services API Test Page</h1>
        
        <div class="test-section info">
            <h3>API Endpoints Test</h3>
            <button onclick="testAllServices()">Test All Services</button>
            <button onclick="testMaintenanceServices()">Test Maintenance Services</button>
            <button onclick="testCleaningServices()">Test Cleaning Services</button>
            <button onclick="testSecurityServices()">Test Security Services</button>
            <button onclick="testProvidersEndpoint()">Test Providers Endpoint</button>
        </div>
        
        <div id="results"></div>
        
        <div class="test-section">
            <h3>📋 Test Results Log</h3>
            <pre id="logOutput"></pre>
        </div>
    </div>

    <script>
        let logOutput = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput += `[${timestamp}] ${message}\n`;
            document.getElementById('logOutput').textContent = logOutput;
            console.log(message);
        }
        
        function displayResults(title, data, success = true) {
            const resultsDiv = document.getElementById('results');
            const className = success ? 'success' : 'error';
            
            let content = `
                <div class="test-section ${className}">
                    <h3>${title}</h3>
            `;
            
            if (success && Array.isArray(data)) {
                content += `<p><strong>Found ${data.length} services:</strong></p>`;
                data.forEach(service => {
                    content += `
                        <div class="service-card">
                            <strong>${service.name}</strong> (${service.service_type})<br>
                            <small>${service.description}</small><br>
                            <small>📞 ${service.contact_number} | 📧 ${service.email}</small>
                        </div>
                    `;
                });
            } else if (!success) {
                content += `<p><strong>Error:</strong> ${data}</p>`;
            }
            
            content += `<pre>${JSON.stringify(data, null, 2)}</pre></div>`;
            resultsDiv.innerHTML = content;
        }
        
        async function testAPI(url, title) {
            log(`Testing: ${title} - ${url}`);
            try {
                const response = await fetch(url);
                log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Success: ${data.length} results`);
                    displayResults(title, data, true);
                    return data;
                } else {
                    const errorText = await response.text();
                    log(`Error: HTTP ${response.status} - ${errorText}`);
                    displayResults(title, `HTTP ${response.status}: ${errorText}`, false);
                    return null;
                }
            } catch (error) {
                log(`Network error: ${error.message}`);
                displayResults(title, `Network error: ${error.message}`, false);
                return null;
            }
        }
        
        async function testAllServices() {
            await testAPI('/api/v1/services/', '🔧 All Services');
        }
        
        async function testMaintenanceServices() {
            await testAPI('/api/v1/services/?category=maintenance', '🔨 Maintenance Services');
        }
        
        async function testCleaningServices() {
            await testAPI('/api/v1/services/?category=cleaning', '🧽 Cleaning Services');
        }
        
        async function testSecurityServices() {
            await testAPI('/api/v1/services/?category=security', '🛡️ Security Services');
        }
        
        async function testProvidersEndpoint() {
            await testAPI('/api/v1/services/providers', '👥 Service Providers');
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('=== Services API Test Started ===');
            testAllServices();
        });
    </script>
</body>
</html>
