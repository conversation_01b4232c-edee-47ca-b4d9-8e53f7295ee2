#!/usr/bin/env python3
"""
Script to create sample service providers for testing
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models
from app.db.session import engine

# Create database tables if they don't exist
models.Base.metadata.create_all(bind=engine)

def create_sample_services():
    """Create sample service providers"""
    db = next(get_db())
    
    # Check if services already exist
    existing_count = db.query(models.ServiceProvider).count()
    if existing_count > 0:
        print(f"✓ Found {existing_count} existing service providers")
        return
    
    sample_services = [
        {
            "name": "QuickFix Maintenance",
            "service_type": "maintenance",
            "description": "Professional home maintenance and repair services",
            "contact_number": "+919876543210",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "CleanPro Services",
            "service_type": "cleaning",
            "description": "Deep cleaning and regular housekeeping services",
            "contact_number": "+919876543211",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "SecureGuard",
            "service_type": "security",
            "description": "24/7 security services for residential properties",
            "contact_number": "+919876543212",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "GreenThumb Landscaping",
            "service_type": "landscaping",
            "description": "Garden design, maintenance, and landscaping services",
            "contact_number": "+919876543213",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "ElectroFix",
            "service_type": "electrical",
            "description": "Licensed electricians for all electrical work",
            "contact_number": "+919876543214",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "PlumbPro",
            "service_type": "plumbing",
            "description": "Emergency plumbing and water system repairs",
            "contact_number": "+919876543215",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "PaintMasters",
            "service_type": "painting",
            "description": "Interior and exterior painting services",
            "contact_number": "+919876543216",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "CoolAir HVAC",
            "service_type": "hvac",
            "description": "Air conditioning and heating system services",
            "contact_number": "+919876543217",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "MoveMate",
            "service_type": "moving",
            "description": "Professional moving and relocation services",
            "contact_number": "+919876543218",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "PestAway",
            "service_type": "pest_control",
            "description": "Safe and effective pest control solutions",
            "contact_number": "+919876543219",
            "email": "<EMAIL>",
            "is_verified": True
        }
    ]
    
    print("🔧 Creating sample service providers...")
    
    for service_data in sample_services:
        service = models.ServiceProvider(**service_data)
        db.add(service)
        print(f"  ✓ Added {service_data['name']} ({service_data['service_type']})")
    
    try:
        db.commit()
        print(f"\n✅ Successfully created {len(sample_services)} service providers!")
        
        # Verify creation
        total_count = db.query(models.ServiceProvider).count()
        print(f"📊 Total service providers in database: {total_count}")
        
        # Show breakdown by category
        print("\n📋 Services by category:")
        categories = db.query(models.ServiceProvider.service_type).distinct().all()
        for (category,) in categories:
            count = db.query(models.ServiceProvider).filter(
                models.ServiceProvider.service_type == category
            ).count()
            print(f"  • {category}: {count} providers")
            
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating services: {e}")
    finally:
        db.close()

def test_services_api():
    """Test the services API endpoint"""
    import requests
    
    print("\n🧪 Testing Services API...")
    
    base_url = "http://localhost:8000/api/v1/services"
    
    test_cases = [
        {"url": f"{base_url}/", "name": "All services"},
        {"url": f"{base_url}/?category=maintenance", "name": "Maintenance services"},
        {"url": f"{base_url}/?category=cleaning", "name": "Cleaning services"},
        {"url": f"{base_url}/?category=security", "name": "Security services"},
    ]
    
    for test in test_cases:
        try:
            response = requests.get(test["url"], timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ {test['name']}: {len(data)} results")
            else:
                print(f"  ❌ {test['name']}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"  ❌ {test['name']}: Connection error - {e}")

if __name__ == "__main__":
    print("🏗️  Service Provider Setup Tool")
    print("=" * 50)
    
    create_sample_services()
    
    # Test API if server is running
    try:
        test_services_api()
    except Exception as e:
        print(f"\n⚠️  Could not test API (server might not be running): {e}")
        print("💡 Start the server with: python run_server.py")
    
    print("\n✅ Setup complete!")
    print("🌐 You can now test the services API at:")
    print("   • http://localhost:8000/api/v1/services/")
    print("   • http://localhost:8000/api/v1/services/?category=maintenance")
    print("   • http://localhost:8000/api/docs (API documentation)")
