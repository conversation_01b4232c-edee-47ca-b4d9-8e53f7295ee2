<!DOCTYPE html>
<html>
<head>
    <title>🔄 Custom Token to ID Token Converter</title>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            max-width: 1000px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            background: #f9f9f9;
        }
        .token { 
            background: #f0f0f0; 
            padding: 10px; 
            word-break: break-all; 
            margin: 10px 0; 
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            border: none; 
            cursor: pointer; 
            border-radius: 3px;
        }
        button:hover { background: #0056b3; }
        textarea { width: 100%; margin: 5px 0; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>🔄 Custom Token to ID Token Converter</h1>
    <p><strong>Purpose:</strong> Convert Firebase custom tokens to ID tokens for API authentication</p>
    
    <div class="section">
        <h3>📋 Step 1: Update Firebase Configuration</h3>
        <p>Replace this with your actual Firebase project configuration:</p>
        <textarea id="config" rows="8">{
  "apiKey": "your-api-key-here",
  "authDomain": "your-project.firebaseapp.com",
  "projectId": "your-project-id",
  "storageBucket": "your-project.appspot.com",
  "messagingSenderId": "123456789",
  "appId": "your-app-id"
}</textarea>
        <button onclick="initFirebase()">Initialize Firebase</button>
        <div id="init-status"></div>
    </div>
    
    <div class="section">
        <h3>🔑 Step 2: Custom Token (Pre-filled)</h3>
        <p>This is the custom token from your Python script:</p>
        <div class="token" id="custom-token-display">eyJhbGciOiAiUlMyNTYiLCAidHlwIjogIkpXVCIsICJraWQiOiAiN2VjMWI1YmZmN2FhYmEwNWIxMzFlOWI4YWQ0ZWI0NzhkMzQ1YzY4ZCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ESD6hD9LpvbmycZebxkIM5GxmbGWuZ8tHYRB-PHNiqlx8wH3H__hd0wWKeB3JpdjYZWRV4JxIhOm8k74YqOM2q6acRb8PXns7nl_EAPMqTOPmHpZLfGJNdY5rH9PryV5ddfbB56jIS8VjzY-nsouX9-RgQRRNvdmIAGD8XIrOCvQhnJiMGL0XT_PM9g1lTlLC64hLkB3crChGV0UOj1-2MZ3mb8G8OjXUpPxjQaUm5De3SnStl3Q6--SIF2lo6mvkZl9cOCIkSG3WoykaeIWAgKO8ldHAD4nnPKBbhSM1KHZRJpMdX0T81KKf3hkCNN6Qlh5Yjuc1R6N6MQv7JUIYg</div>
        <button onclick="convertToken()">Convert to ID Token</button>
        <div id="convert-status"></div>
    </div>
    
    <div class="section">
        <h3>🎯 Step 3: ID Token Result</h3>
        <p>The ID token will appear here (this is what you use for API calls):</p>
        <textarea id="id-token" rows="8" readonly placeholder="ID token will appear here after conversion..."></textarea>
        <button onclick="copyToken()">Copy ID Token</button>
        <button onclick="testAPI()">Test API with ID Token</button>
        <div id="api-status"></div>
    </div>

    <div class="section">
        <h3>📝 Step 4: How to Use the ID Token</h3>
        <div id="usage-instructions">
            <p>Once you have the ID token, use it in your API calls like this:</p>
            <pre>curl -X POST http://127.0.0.1:8000/api/v1/auth/login \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"id_token": "YOUR_ID_TOKEN"}'</pre>
        </div>
    </div>

    <script>
        let auth = null;
        const customToken = "eyJhbGciOiAiUlMyNTYiLCAidHlwIjogIkpXVCIsICJraWQiOiAiN2VjMWI1YmZmN2FhYmEwNWIxMzFlOWI4YWQ0ZWI0NzhkMzQ1YzY4ZCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ESD6hD9LpvbmycZebxkIM5GxmbGWuZ8tHYRB-PHNiqlx8wH3H__hd0wWKeB3JpdjYZWRV4JxIhOm8k74YqOM2q6acRb8PXns7nl_EAPMqTOPmHpZLfGJNdY5rH9PryV5ddfbB56jIS8VjzY-nsouX9-RgQRRNvdmIAGD8XIrOCvQhnJiMGL0XT_PM9g1lTlLC64hLkB3crChGV0UOj1-2MZ3mb8G8OjXUpPxjQaUm5De3SnStl3Q6--SIF2lo6mvkZl9cOCIkSG3WoykaeIWAgKO8ldHAD4nnPKBbhSM1KHZRJpMdX0T81KKf3hkCNN6Qlh5Yjuc1R6N6MQv7JUIYg";
        
        function initFirebase() {
            try {
                const configText = document.getElementById('config').value;
                const firebaseConfig = JSON.parse(configText);
                
                // Check if config looks valid
                if (!firebaseConfig.apiKey || firebaseConfig.apiKey === "your-api-key-here") {
                    document.getElementById('init-status').innerHTML = 
                        '<div class="warning">⚠️ Please update the Firebase config with your actual project settings!</div>';
                    return;
                }
                
                firebase.initializeApp(firebaseConfig);
                auth = firebase.auth();
                
                document.getElementById('init-status').innerHTML = 
                    '<div class="success">✅ Firebase initialized successfully!</div>';
                    
            } catch (error) {
                document.getElementById('init-status').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            }
        }
        
        function convertToken() {
            if (!auth) {
                alert('Please initialize Firebase first!');
                return;
            }
            
            document.getElementById('convert-status').innerHTML = 
                '<div class="info">🔄 Converting custom token to ID token...</div>';
            
            auth.signInWithCustomToken(customToken)
                .then((userCredential) => {
                    document.getElementById('convert-status').innerHTML = 
                        '<div class="info">🔄 Getting ID token...</div>';
                    return userCredential.user.getIdToken();
                })
                .then((idToken) => {
                    document.getElementById('id-token').value = idToken;
                    document.getElementById('convert-status').innerHTML = 
                        '<div class="success">✅ ID token generated successfully! You can now use this for API calls.</div>';
                    console.log('ID Token:', idToken);
                    
                    // Show usage example with actual token
                    updateUsageInstructions(idToken);
                })
                .catch((error) => {
                    document.getElementById('convert-status').innerHTML = 
                        '<div class="error">❌ Error: ' + error.message + '</div>';
                    console.error('Error:', error);
                });
        }
        
        function copyToken() {
            const idToken = document.getElementById('id-token').value;
            if (!idToken) {
                alert('Please convert the token first!');
                return;
            }
            
            navigator.clipboard.writeText(idToken).then(() => {
                alert('ID token copied to clipboard!');
            }).catch(() => {
                // Fallback for older browsers
                document.getElementById('id-token').select();
                document.execCommand('copy');
                alert('ID token copied to clipboard!');
            });
        }
        
        function testAPI() {
            const idToken = document.getElementById('id-token').value;
            if (!idToken) {
                alert('Please convert the token first!');
                return;
            }
            
            document.getElementById('api-status').innerHTML = 
                '<div class="info">🔄 Testing API...</div>';
            
            fetch('http://127.0.0.1:8000/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + idToken
                },
                body: JSON.stringify({ id_token: idToken })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('api-status').innerHTML = 
                    '<div class="success">✅ API Test Successful!</div>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('api-status').innerHTML = 
                    '<div class="error">❌ API Test Failed: ' + error.message + '</div>';
            });
        }
        
        function updateUsageInstructions(idToken) {
            const shortToken = idToken.substring(0, 50) + '...';
            document.getElementById('usage-instructions').innerHTML = `
                <p>✅ <strong>Success!</strong> Use this ID token in your API calls:</p>
                <h4>Example curl command:</h4>
                <pre>curl -X POST http://127.0.0.1:8000/api/v1/auth/login \
  -H "Authorization: Bearer ${shortToken}" \
  -H "Content-Type: application/json" \
  -d '{"id_token": "${shortToken}"}'</pre>
                
                <h4>Example JavaScript:</h4>
                <pre>fetch('http://127.0.0.1:8000/api/v1/auth/login', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ${shortToken}',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ id_token: '${shortToken}' })
});</pre>
                
                <p><strong>Note:</strong> The ID token expires in 1 hour. Generate a new one when needed.</p>
            `;
        }
        
        // Show initial instructions
        window.onload = function() {
            document.getElementById('usage-instructions').innerHTML += 
                '<p class="warning">⚠️ <strong>Important:</strong> You need to convert the custom token to an ID token first!</p>';
        };
    </script>
</body>
</html>