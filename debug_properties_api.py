#!/usr/bin/env python3
"""
Debug script to check properties API issues
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models, crud
from app.schemas.properties import PropertyOut

def debug_properties():
    """Debug properties database and API"""
    db = next(get_db())
    
    print("🔍 Debugging Properties API Issues")
    print("=" * 50)
    
    # 1. Check if properties exist in database
    print("\n1. 📊 Database Check:")
    properties = db.query(models.Property).all()
    print(f"   Total properties in database: {len(properties)}")
    
    if properties:
        for i, prop in enumerate(properties[:3], 1):
            print(f"   {i}. {prop.title} - {prop.city}")
            print(f"      ID: {prop.id}, Type: {prop.property_type}")
            print(f"      Owner ID: {prop.owner_id}")
    
    # 2. Test CRUD function
    print("\n2. 🔧 CRUD Function Test:")
    try:
        crud_properties = crud.get_properties(db, skip=0, limit=10)
        print(f"   CRUD returned: {len(crud_properties)} properties")
        
        if crud_properties:
            for prop in crud_properties[:2]:
                print(f"   - {prop.title}")
        
    except Exception as e:
        print(f"   ❌ CRUD Error: {e}")
    
    # 3. Test schema validation
    print("\n3. 📋 Schema Validation Test:")
    if properties:
        for prop in properties[:2]:
            try:
                validated = PropertyOut.model_validate(prop)
                print(f"   ✅ {prop.title} - Schema validation passed")
            except Exception as e:
                print(f"   ❌ {prop.title} - Schema validation failed: {e}")
    
    # 4. Test search endpoint logic
    print("\n4. 🔍 Search Endpoint Logic Test:")
    try:
        # Simulate search endpoint logic
        filters = {}
        search_properties = crud.get_properties(db, skip=0, limit=100, filters=filters)
        print(f"   Search logic returned: {len(search_properties)} properties")
        
        # Test schema validation for search results
        property_results = []
        for prop in search_properties:
            try:
                property_results.append(PropertyOut.model_validate(prop))
            except Exception as e:
                print(f"   ❌ Schema validation failed for {prop.title}: {e}")
        
        print(f"   Successfully validated: {len(property_results)} properties")
        
    except Exception as e:
        print(f"   ❌ Search logic error: {e}")
    
    # 5. Check property relationships
    print("\n5. 🔗 Relationship Check:")
    if properties:
        prop = properties[0]
        try:
            print(f"   Property: {prop.title}")
            print(f"   Owner: {prop.owner}")
            print(f"   Images: {len(prop.images) if prop.images else 0}")
            print(f"   Videos: {len(prop.videos) if hasattr(prop, 'videos') and prop.videos else 0}")
            print(f"   Features: {len(prop.features) if prop.features else 0}")
        except Exception as e:
            print(f"   ❌ Relationship error: {e}")
    
    db.close()

def test_api_directly():
    """Test API endpoints directly"""
    import requests
    
    print("\n6. 🌐 Direct API Test:")
    base_url = "http://localhost:8000/api/v1"
    
    endpoints = [
        "/search/",
        "/properties/",
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            print(f"   {endpoint:<15} Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if endpoint == "/search/":
                    print(f"                   Properties: {len(data.get('properties', []))}")
                elif endpoint == "/properties/":
                    print(f"                   Properties: {len(data) if isinstance(data, list) else 'Not a list'}")
            else:
                print(f"                   Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   {endpoint:<15} Error: {e}")

def fix_property_issues():
    """Try to fix common property issues"""
    db = next(get_db())
    
    print("\n7. 🔧 Fixing Property Issues:")
    
    # Check for properties with missing owners
    properties_without_owners = db.query(models.Property).filter(
        models.Property.owner_id == None
    ).all()
    
    if properties_without_owners:
        print(f"   Found {len(properties_without_owners)} properties without owners")
        
        # Get a valid owner
        owner = db.query(models.User).first()
        if owner:
            for prop in properties_without_owners:
                prop.owner_id = owner.id
                print(f"   Fixed owner for: {prop.title}")
            
            db.commit()
            print("   ✅ Fixed owner issues")
        else:
            print("   ❌ No users found to assign as owners")
    else:
        print("   ✅ All properties have owners")
    
    db.close()

if __name__ == "__main__":
    debug_properties()
    test_api_directly()
    
    response = input("\n❓ Would you like to try fixing property issues? (y/n): ").lower()
    if response == 'y':
        fix_property_issues()
        print("\n🔄 Re-testing after fixes:")
        test_api_directly()
    
    print("\n✅ Debug complete!")
