#!/usr/bin/env python3
"""
Script to check and clean up service providers data
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models

def check_services_data():
    """Check current services data"""
    db = next(get_db())
    
    print("🔍 Current Service Providers in Database:")
    print("=" * 60)
    
    services = db.query(models.ServiceProvider).all()
    
    for i, service in enumerate(services, 1):
        print(f"{i}. Name: {service.name}")
        print(f"   Type: {service.service_type}")
        print(f"   Description: {service.description}")
        print(f"   Contact: {service.contact_number}")
        print(f"   Email: {service.email}")
        print(f"   Verified: {service.is_verified}")
        print(f"   ID: {service.id}")
        print("-" * 40)
    
    print(f"\n📊 Total: {len(services)} service providers")
    
    # Group by service type
    print("\n📋 Services by Type:")
    types = {}
    for service in services:
        if service.service_type not in types:
            types[service.service_type] = []
        types[service.service_type].append(service.name)
    
    for service_type, names in types.items():
        print(f"  • {service_type}: {len(names)} providers")
        for name in names:
            print(f"    - {name}")
    
    db.close()
    return services

def clean_duplicate_services():
    """Remove duplicate or generic service entries"""
    db = next(get_db())
    
    print("\n🧹 Cleaning up service providers...")
    
    # Find services with generic names or duplicates
    generic_names = [
        "Professional Service",
        "Service Provider", 
        "Professional Services",
        "General Service"
    ]
    
    deleted_count = 0
    
    for generic_name in generic_names:
        services_to_delete = db.query(models.ServiceProvider).filter(
            models.ServiceProvider.name.like(f"%{generic_name}%")
        ).all()
        
        for service in services_to_delete:
            print(f"  🗑️  Deleting: {service.name} ({service.service_type})")
            db.delete(service)
            deleted_count += 1
    
    # Find exact duplicates by name and service_type
    all_services = db.query(models.ServiceProvider).all()
    seen = set()
    duplicates = []
    
    for service in all_services:
        key = (service.name.lower(), service.service_type.lower())
        if key in seen:
            duplicates.append(service)
            print(f"  🗑️  Duplicate found: {service.name} ({service.service_type})")
        else:
            seen.add(key)
    
    for duplicate in duplicates:
        db.delete(duplicate)
        deleted_count += 1
    
    if deleted_count > 0:
        db.commit()
        print(f"✅ Deleted {deleted_count} duplicate/generic services")
    else:
        print("✅ No duplicates or generic services found")
    
    db.close()

def create_quality_services():
    """Create high-quality, specific service providers"""
    db = next(get_db())
    
    # Check if we already have good services
    existing_count = db.query(models.ServiceProvider).count()
    if existing_count >= 10:
        print(f"✓ Already have {existing_count} services, skipping creation")
        db.close()
        return
    
    quality_services = [
        {
            "name": "QuickFix Maintenance Co.",
            "service_type": "maintenance",
            "description": "24/7 emergency repairs, plumbing, electrical, and general maintenance for residential properties",
            "contact_number": "+919876543210",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "SparkClean Professional",
            "service_type": "cleaning",
            "description": "Deep cleaning, regular housekeeping, carpet cleaning, and sanitization services",
            "contact_number": "+919876543211",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "SecureGuard Services",
            "service_type": "security",
            "description": "Trained security guards, CCTV monitoring, and access control systems",
            "contact_number": "+919876543212",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "GreenScape Landscaping",
            "service_type": "landscaping",
            "description": "Garden design, lawn maintenance, tree trimming, and outdoor beautification",
            "contact_number": "+919876543213",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "PowerTech Electricians",
            "service_type": "electrical",
            "description": "Licensed electricians for wiring, repairs, installations, and electrical safety audits",
            "contact_number": "+919876543214",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "FlowMaster Plumbing",
            "service_type": "plumbing",
            "description": "Expert plumbers for pipe repairs, bathroom fittings, water heater installation",
            "contact_number": "+919876543215",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "ColorCraft Painters",
            "service_type": "painting",
            "description": "Interior and exterior painting, wall textures, and decorative finishes",
            "contact_number": "+919876543216",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "CoolBreeze HVAC",
            "service_type": "hvac",
            "description": "AC installation, repair, maintenance, and ventilation system services",
            "contact_number": "+919876543217",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "LegalEase Consultants",
            "service_type": "legal",
            "description": "Property documentation, legal advice, contract review, and dispute resolution",
            "contact_number": "+919876543218",
            "email": "<EMAIL>",
            "is_verified": True
        },
        {
            "name": "WealthWise Financial",
            "service_type": "financial",
            "description": "Home loans, investment planning, insurance, and financial advisory services",
            "contact_number": "+919876543219",
            "email": "<EMAIL>",
            "is_verified": True
        }
    ]
    
    print(f"\n🏗️  Creating {len(quality_services)} quality service providers...")
    
    for service_data in quality_services:
        # Check if service already exists
        existing = db.query(models.ServiceProvider).filter(
            models.ServiceProvider.name == service_data["name"]
        ).first()
        
        if not existing:
            service = models.ServiceProvider(**service_data)
            db.add(service)
            print(f"  ✅ Added: {service_data['name']}")
        else:
            print(f"  ⏭️  Skipped: {service_data['name']} (already exists)")
    
    try:
        db.commit()
        print("✅ Quality services created successfully!")
    except Exception as e:
        db.rollback()
        print(f"❌ Error creating services: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Service Provider Data Management Tool")
    print("=" * 50)
    
    # Check current data
    services = check_services_data()
    
    # Clean up if needed
    if len(services) > 0:
        response = input("\n❓ Would you like to clean up duplicate/generic services? (y/n): ").lower()
        if response == 'y':
            clean_duplicate_services()
            print("\n" + "="*50)
            print("📊 Data after cleanup:")
            check_services_data()
    
    # Create quality services if needed
    response = input("\n❓ Would you like to create quality service providers? (y/n): ").lower()
    if response == 'y':
        create_quality_services()
        print("\n" + "="*50)
        print("📊 Final data:")
        check_services_data()
    
    print("\n✅ Service management complete!")
