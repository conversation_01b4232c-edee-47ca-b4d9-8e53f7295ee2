<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .about-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 40px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 60px;
        }

        .hero-section h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .hero-section p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            text-align: center;
            margin-bottom: 40px;
        }

        .section-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .stat-card {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .team-member {
            background: white;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .member-avatar {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .member-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .member-role {
            color: #667eea;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .member-description {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .mission-vision {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
        }

        .mission-card, .vision-card {
            padding: 40px;
            border-radius: 20px;
            text-align: center;
        }

        .mission-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .vision-card {
            background: #f8f9fa;
            color: #333;
        }

        .mission-card h3, .vision-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .mission-card p, .vision-card p {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            border-radius: 20px;
            text-align: center;
        }

        .contact-section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .contact-section p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .contact-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .contact-btn {
            background: white;
            color: #667eea;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .about-container {
                padding: 15px;
            }

            .hero-section {
                padding: 60px 30px;
            }

            .hero-section h1 {
                font-size: 2.5rem;
            }

            .section-content {
                padding: 30px 20px;
            }

            .mission-vision {
                grid-template-columns: 1fr;
            }

            .contact-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Include Navbar -->
    {% include 'components/navbar.html' %}

    <div class="about-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1>About DreamBig Real Estate</h1>
            <p>We're revolutionizing the real estate industry with cutting-edge technology, AI-powered insights, and a commitment to making property transactions transparent, secure, and efficient for everyone.</p>
        </div>

        <!-- Mission & Vision -->
        <div class="section">
            <div class="mission-vision">
                <div class="mission-card">
                    <h3>Our Mission</h3>
                    <p>To democratize real estate by providing a transparent, secure, and intelligent platform that connects buyers, sellers, investors, and service providers while leveraging AI to deliver personalized experiences and insights.</p>
                </div>
                <div class="vision-card">
                    <h3>Our Vision</h3>
                    <p>To become the most trusted and innovative real estate platform in India, where every property transaction is seamless, every investment decision is informed, and every dream home becomes a reality.</p>
                </div>
            </div>
        </div>

        <!-- Key Features -->
        <div class="section">
            <h2 class="section-title">Why Choose DreamBig?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="feature-title">AI-Powered Intelligence</div>
                    <div class="feature-description">
                        Advanced AI algorithms provide personalized property recommendations, fraud detection, and market insights to help you make informed decisions.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-title">Verified & Secure</div>
                    <div class="feature-description">
                        Comprehensive KYC verification, document authentication, and secure transactions ensure a safe and trustworthy platform for all users.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="feature-title">Investment Analytics</div>
                    <div class="feature-description">
                        Detailed investment analytics, ROI calculations, and market trends help investors make data-driven decisions and maximize returns.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <div class="feature-title">Professional Services</div>
                    <div class="feature-description">
                        Access to verified legal, financial, and maintenance service providers to support every aspect of your real estate journey.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-title">Mobile-First Experience</div>
                    <div class="feature-description">
                        Responsive design and mobile optimization ensure you can access all features seamlessly from any device, anywhere.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="feature-title">Community Driven</div>
                    <div class="feature-description">
                        Join a community of property enthusiasts, investors, and professionals sharing insights and experiences.
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="section">
            <div class="section-content">
                <h2 class="section-title">Our Impact</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">10,000+</div>
                        <div class="stat-label">Properties Listed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5,000+</div>
                        <div class="stat-label">Happy Customers</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">₹500Cr+</div>
                        <div class="stat-label">Transactions Facilitated</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Cities Covered</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team -->
        <div class="section">
            <h2 class="section-title">Meet Our Team</h2>
            <div class="section-content">
                <div class="team-grid">
                    <div class="team-member">
                        <div class="member-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="member-name">Rajesh Kumar</div>
                        <div class="member-role">CEO & Founder</div>
                        <div class="member-description">
                            15+ years in real estate and technology. Passionate about transforming the property market through innovation.
                        </div>
                    </div>

                    <div class="team-member">
                        <div class="member-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="member-name">Priya Sharma</div>
                        <div class="member-role">CTO</div>
                        <div class="member-description">
                            AI and machine learning expert with 12+ years in building scalable technology platforms.
                        </div>
                    </div>

                    <div class="team-member">
                        <div class="member-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="member-name">Amit Patel</div>
                        <div class="member-role">Head of Operations</div>
                        <div class="member-description">
                            Operations specialist ensuring smooth platform functioning and exceptional user experience.
                        </div>
                    </div>

                    <div class="team-member">
                        <div class="member-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="member-name">Sneha Reddy</div>
                        <div class="member-role">Head of Customer Success</div>
                        <div class="member-description">
                            Dedicated to ensuring every customer achieves their real estate goals with our platform.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Section -->
        <div class="contact-section">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of satisfied customers who have found their dream properties with DreamBig</p>
            <div class="contact-buttons">
                <a href="/register" class="contact-btn">
                    <i class="fas fa-user-plus"></i>
                    Sign Up Today
                </a>
                <a href="/properties" class="contact-btn">
                    <i class="fas fa-search"></i>
                    Browse Properties
                </a>
                <a href="mailto:<EMAIL>" class="contact-btn">
                    <i class="fas fa-envelope"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
</body>
</html>
