#!/usr/bin/env python3
"""
Firebase Token Generation Script
This script shows different ways to get Firebase tokens for testing your API
"""

import requests
import json
import firebase_admin
from firebase_admin import credentials, auth

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Use your Firebase credentials file
        cred = credentials.Certificate("app/dreambig_firebase_credentioal.json")
        firebase_admin.initialize_app(cred)
        print("✅ Firebase Admin SDK initialized")
        return True
    except Exception as e:
        print(f"❌ Error initializing Firebase: {e}")
        return False

def create_custom_token_for_user(firebase_uid, user_data=None):
    """Create a custom token for a specific Firebase UID"""
    try:
        custom_claims = user_data or {}
        custom_token = auth.create_custom_token(firebase_uid, custom_claims)
        return custom_token.decode('utf-8')
    except Exception as e:
        print(f"❌ Error creating custom token: {e}")
        return None

def get_user_from_api(user_id):
    """Get user information from your API"""
    try:
        response = requests.get(f'http://127.0.0.1:8000/api/v1/auth/users-firebase-status')
        if response.status_code == 200:
            result = response.json()
            for user in result['users']:
                if user['id'] == user_id and user['firebase_status'] == 'exists':
                    return user
        return None
    except Exception as e:
        print(f"❌ Error getting user from API: {e}")
        return None

def test_api_with_token(token):
    """Test your API with the token"""
    try:
        # Test the login endpoint
        response = requests.post(
            'http://127.0.0.1:8000/api/v1/auth/login',
            headers={'Authorization': f'Bearer {token}'},
            json={'id_token': token}
        )
        
        print(f"API Test Response Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ API Authentication successful!")
            print(f"User: {result.get('user', {}).get('email', 'Unknown')}")
            return True
        else:
            print(f"❌ API Authentication failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def main():
    print("🔥 Firebase Token Generator for API Testing")
    print("=" * 50)
    
    # Initialize Firebase
    if not initialize_firebase():
        return
    
    # Get available users
    print("\n📋 Available Firebase Users:")
    try:
        response = requests.get('http://127.0.0.1:8000/api/v1/auth/users-firebase-status')
        if response.status_code == 200:
            result = response.json()
            firebase_users = [user for user in result['users'] if user['firebase_status'] == 'exists']
            
            if not firebase_users:
                print("❌ No Firebase users found. Please register a user first.")
                return
            
            for i, user in enumerate(firebase_users, 1):
                print(f"{i}. ID: {user['id']}, Email: {user['email']}, Firebase UID: {user['firebase_uid']}")
            
            # Let user choose
            try:
                choice = int(input(f"\nSelect user (1-{len(firebase_users)}): ")) - 1
                if 0 <= choice < len(firebase_users):
                    selected_user = firebase_users[choice]
                else:
                    print("❌ Invalid choice")
                    return
            except ValueError:
                print("❌ Invalid input")
                return
            
        else:
            print("❌ Could not get users from API")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    print(f"\n🎯 Selected User: {selected_user['email']}")
    
    # Create custom token
    print("\n🔑 Creating custom token...")
    custom_claims = {
        'role': 'tenant',  # You can customize this
        'email': selected_user['email'],
        'user_id': selected_user['id']
    }
    
    custom_token = create_custom_token_for_user(
        selected_user['firebase_uid'], 
        custom_claims
    )
    
    if custom_token:
        print("✅ Custom token created successfully!")
        print(f"\n📋 Custom Token:")
        print(f"{custom_token}")
        
        print(f"\n📋 How to use this token:")
        print("1. Use this custom token in your frontend to sign in to Firebase")
        print("2. After signing in, call user.getIdToken() to get the ID token")
        print("3. Use the ID token in API requests with Authorization header")
        print("\n💡 Example API call:")
        print(f"curl -X POST http://127.0.0.1:8000/api/v1/auth/login \\")
        print(f"  -H 'Authorization: Bearer <ID_TOKEN_FROM_FRONTEND>' \\")
        print(f"  -H 'Content-Type: application/json' \\")
        print(f"  -d '{{\"id_token\": \"<ID_TOKEN_FROM_FRONTEND>\"}}'")
        
        print(f"\n🌐 For web testing, use the HTML file: firebase_auth_example.html")
        print(f"   - Replace the Firebase config with your project settings")
        print(f"   - Use email: {selected_user['email']} (you'll need to set a password)")
        
    else:
        print("❌ Failed to create custom token")

if __name__ == "__main__":
    main()
