#!/usr/bin/env python3
"""
Fix property timestamps and validation issues
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models
from datetime import datetime

def fix_property_timestamps():
    """Fix missing updated_at timestamps"""
    db = next(get_db())
    
    print("🔧 Fixing property timestamps...")
    
    properties = db.query(models.Property).all()
    fixed_count = 0
    
    for prop in properties:
        if prop.updated_at is None:
            prop.updated_at = prop.created_at or datetime.utcnow()
            fixed_count += 1
            print(f"  ✅ Fixed timestamp for: {prop.title}")
    
    if fixed_count > 0:
        db.commit()
        print(f"✅ Fixed {fixed_count} property timestamps")
    else:
        print("✅ All properties already have valid timestamps")
    
    db.close()

def test_schema_validation():
    """Test schema validation after fixes"""
    from app.schemas.properties import PropertyOut
    
    db = next(get_db())
    
    print("\n🧪 Testing schema validation...")
    
    properties = db.query(models.Property).all()
    valid_count = 0
    
    for prop in properties:
        try:
            validated = PropertyOut.model_validate(prop)
            valid_count += 1
            print(f"  ✅ {prop.title} - Valid")
        except Exception as e:
            print(f"  ❌ {prop.title} - Error: {e}")
    
    print(f"\n📊 Validation Results: {valid_count}/{len(properties)} properties valid")
    
    db.close()
    return valid_count == len(properties)

if __name__ == "__main__":
    print("🔧 Property Validation Fixer")
    print("=" * 40)
    
    fix_property_timestamps()
    success = test_schema_validation()
    
    if success:
        print("\n🎉 All properties are now valid!")
        print("🔄 The API should work now.")
    else:
        print("\n⚠️  Some properties still have validation issues.")
    
    print("\n✅ Fix complete!")
