#!/usr/bin/env python3
"""
API Testing Script with Firebase Tokens
This script demonstrates how to test your API endpoints with Firebase ID tokens
"""

import requests
import json
import firebase_admin
from firebase_admin import credentials, auth
import time

def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        cred = credentials.Certificate("app/dreambig_firebase_credentioal.json")
        firebase_admin.initialize_app(cred)
        return True
    except Exception as e:
        print(f"❌ Error initializing Firebase: {e}")
        return False

def create_custom_token(firebase_uid, claims=None):
    """Create a custom token"""
    try:
        custom_token = auth.create_custom_token(firebase_uid, claims or {})
        return custom_token.decode('utf-8')
    except Exception as e:
        print(f"❌ Error creating custom token: {e}")
        return None

def verify_id_token(id_token):
    """Verify an ID token using Firebase Admin SDK"""
    try:
        decoded_token = auth.verify_id_token(id_token)
        return decoded_token
    except Exception as e:
        print(f"❌ Error verifying ID token: {e}")
        return None

def test_api_endpoints(id_token):
    """Test various API endpoints with the ID token"""
    headers = {
        'Authorization': f'Bearer {id_token}',
        'Content-Type': 'application/json'
    }
    
    print("🧪 Testing API Endpoints...")
    print("=" * 50)
    
    # Test 1: Login endpoint
    print("1. Testing /api/v1/auth/login")
    try:
        response = requests.post(
            'http://127.0.0.1:8000/api/v1/auth/login',
            headers=headers,
            json={'id_token': id_token}
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success! User: {result.get('user', {}).get('email', 'Unknown')}")
            user_data = result.get('user', {})
            print(f"   User ID: {user_data.get('id')}")
            print(f"   Role: {user_data.get('role')}")
        else:
            print(f"   ❌ Failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print()
    
    # Test 2: User recommendations (requires authentication)
    print("2. Testing /api/v1/users/recommendations")
    try:
        response = requests.get(
            'http://127.0.0.1:8000/api/v1/users/recommendations',
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success! Recommendations: {len(result.get('recommendations', []))}")
        else:
            print(f"   ❌ Failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print()
    
    # Test 3: User analytics
    print("3. Testing /api/v1/users/analytics")
    try:
        response = requests.get(
            'http://127.0.0.1:8000/api/v1/users/analytics',
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success! Activity score: {result.get('activity_score', 0)}")
        else:
            print(f"   ❌ Failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print()
    
    # Test 4: Search (should work without auth too)
    print("4. Testing /api/v1/search/")
    try:
        response = requests.get(
            'http://127.0.0.1:8000/api/v1/search/',
            headers=headers
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Success! Properties found: {result.get('total', 0)}")
        else:
            print(f"   ❌ Failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    print("🔥 Firebase API Testing Tool")
    print("=" * 50)
    
    # Initialize Firebase
    if not initialize_firebase():
        return
    
    # Get available users
    print("\n📋 Getting Firebase users...")
    try:
        response = requests.get('http://127.0.0.1:8000/api/v1/auth/users-firebase-status')
        if response.status_code == 200:
            result = response.json()
            firebase_users = [user for user in result['users'] if user['firebase_status'] == 'exists']
            
            if not firebase_users:
                print("❌ No Firebase users found. Please register a user first.")
                return
            
            print(f"Found {len(firebase_users)} Firebase users:")
            for i, user in enumerate(firebase_users, 1):
                print(f"{i}. {user['email']} (ID: {user['id']})")
            
            # Use the first user for testing
            selected_user = firebase_users[0]
            print(f"\n🎯 Using user: {selected_user['email']}")
            
        else:
            print("❌ Could not get users from API")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Create custom token
    print("\n🔑 Creating custom token...")
    custom_claims = {
        'role': 'tenant',
        'email': selected_user['email'],
        'user_id': selected_user['id']
    }
    
    custom_token = create_custom_token(selected_user['firebase_uid'], custom_claims)
    if not custom_token:
        print("❌ Failed to create custom token")
        return
    
    print("✅ Custom token created")
    print(f"Custom Token: {custom_token[:50]}...")
    
    # Note: In a real scenario, you would use this custom token in your frontend
    # to sign in to Firebase, then get the ID token. For testing purposes,
    # we'll show you how to verify the custom token structure.
    
    print("\n💡 Next Steps:")
    print("1. Use the custom token in your frontend JavaScript:")
    print("   firebase.auth().signInWithCustomToken(customToken)")
    print("2. After signing in, get the ID token:")
    print("   user.getIdToken().then(idToken => { /* use idToken */ })")
    print("3. Use the ID token in API requests")
    
    print(f"\n🌐 Open test_firebase_token.html in your browser to:")
    print("- Sign in with the custom token")
    print("- Get an ID token")
    print("- Test API endpoints")
    
    print(f"\n📋 Custom token for testing:")
    print(custom_token)

if __name__ == "__main__":
    main()
