#!/usr/bin/env python3
"""
Convert Custom Token to ID Token
This script helps you convert a Firebase custom token to an ID token for API testing
"""

import requests
import json
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
import tempfile
import os

def create_temp_html_file(custom_token, firebase_config):
    """Create a temporary HTML file for token conversion"""
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
</head>
<body>
    <div id="status">Loading...</div>
    <div id="token" style="display:none;"></div>
    
    <script>
        const firebaseConfig = {firebase_config};
        
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        
        const customToken = "{custom_token}";
        
        document.getElementById('status').textContent = 'Signing in with custom token...';
        
        auth.signInWithCustomToken(customToken)
            .then((userCredential) => {{
                document.getElementById('status').textContent = 'Getting ID token...';
                return userCredential.user.getIdToken();
            }})
            .then((idToken) => {{
                document.getElementById('status').textContent = 'Success!';
                document.getElementById('token').textContent = idToken;
                document.getElementById('token').style.display = 'block';
                console.log('ID Token:', idToken);
            }})
            .catch((error) => {{
                document.getElementById('status').textContent = 'Error: ' + error.message;
                console.error('Error:', error);
            }});
    </script>
</body>
</html>
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
        f.write(html_content)
        return f.name

def get_id_token_with_selenium(custom_token, firebase_config):
    """Use Selenium to convert custom token to ID token"""
    try:
        # Create temporary HTML file
        html_file = create_temp_html_file(custom_token, json.dumps(firebase_config))
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Start browser
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            # Load the HTML file
            driver.get(f"file://{html_file}")
            
            # Wait for token to be generated (max 30 seconds)
            wait = WebDriverWait(driver, 30)
            token_element = wait.until(
                EC.presence_of_element_located((By.ID, "token"))
            )
            
            # Wait a bit more for the token to be populated
            time.sleep(2)
            
            # Get the ID token
            id_token = token_element.text
            
            if id_token and id_token != "":
                return id_token
            else:
                # Check for errors
                status = driver.find_element(By.ID, "status").text
                print(f"❌ Error: {status}")
                return None
                
        finally:
            driver.quit()
            # Clean up temporary file
            os.unlink(html_file)
            
    except Exception as e:
        print(f"❌ Error with Selenium: {e}")
        print("💡 Make sure you have Chrome and chromedriver installed")
        return None

def simple_token_converter():
    """Simple method without Selenium - shows manual steps"""
    print("🔄 Simple Token Conversion Method")
    print("=" * 50)
    
    # Get custom token
    print("Step 1: Get your custom token")
    custom_token = input("Paste your custom token here: ").strip()
    
    if not custom_token:
        print("❌ No token provided")
        return
    
    print(f"\n✅ Custom token received (length: {len(custom_token)})")
    
    # Firebase config
    print("\nStep 2: Firebase Configuration")
    print("You need your Firebase project configuration.")
    print("Default config for testing (replace with your actual config):")
    
    firebase_config = {
        "apiKey": "your-api-key",
        "authDomain": "your-project.firebaseapp.com", 
        "projectId": "your-project-id",
        "storageBucket": "your-project.appspot.com",
        "messagingSenderId": "123456789",
        "appId": "your-app-id"
    }
    
    print(json.dumps(firebase_config, indent=2))
    
    # Create HTML file for manual testing
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Token Converter</title>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .token {{ background: #f0f0f0; padding: 10px; word-break: break-all; margin: 10px 0; }}
        button {{ padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }}
    </style>
</head>
<body>
    <h1>🔄 Custom Token to ID Token Converter</h1>
    
    <div>
        <h3>Step 1: Update Firebase Config</h3>
        <textarea id="config" rows="8" cols="80">{json.dumps(firebase_config, indent=2)}</textarea>
        <br><button onclick="initFirebase()">Initialize Firebase</button>
        <div id="init-status"></div>
    </div>
    
    <div>
        <h3>Step 2: Convert Token</h3>
        <div>Custom Token:</div>
        <div class="token">{custom_token}</div>
        <button onclick="convertToken()">Convert to ID Token</button>
        <div id="convert-status"></div>
    </div>
    
    <div>
        <h3>Step 3: ID Token Result</h3>
        <textarea id="id-token" rows="6" cols="80" readonly placeholder="ID token will appear here..."></textarea>
        <br><button onclick="testAPI()">Test API with ID Token</button>
        <div id="api-status"></div>
    </div>

    <script>
        let auth = null;
        const customToken = "{custom_token}";
        
        function initFirebase() {{
            try {{
                const configText = document.getElementById('config').value;
                const firebaseConfig = JSON.parse(configText);
                
                firebase.initializeApp(firebaseConfig);
                auth = firebase.auth();
                
                document.getElementById('init-status').innerHTML = 
                    '<div style="color: green;">✅ Firebase initialized successfully!</div>';
            }} catch (error) {{
                document.getElementById('init-status').innerHTML = 
                    '<div style="color: red;">❌ Error: ' + error.message + '</div>';
            }}
        }}
        
        function convertToken() {{
            if (!auth) {{
                alert('Please initialize Firebase first!');
                return;
            }}
            
            document.getElementById('convert-status').innerHTML = 
                '<div style="color: blue;">🔄 Converting token...</div>';
            
            auth.signInWithCustomToken(customToken)
                .then((userCredential) => {{
                    document.getElementById('convert-status').innerHTML = 
                        '<div style="color: blue;">🔄 Getting ID token...</div>';
                    return userCredential.user.getIdToken();
                }})
                .then((idToken) => {{
                    document.getElementById('id-token').value = idToken;
                    document.getElementById('convert-status').innerHTML = 
                        '<div style="color: green;">✅ ID token generated successfully!</div>';
                    console.log('ID Token:', idToken);
                }})
                .catch((error) => {{
                    document.getElementById('convert-status').innerHTML = 
                        '<div style="color: red;">❌ Error: ' + error.message + '</div>';
                    console.error('Error:', error);
                }});
        }}
        
        function testAPI() {{
            const idToken = document.getElementById('id-token').value;
            if (!idToken) {{
                alert('Please convert the token first!');
                return;
            }}
            
            document.getElementById('api-status').innerHTML = 
                '<div style="color: blue;">🔄 Testing API...</div>';
            
            fetch('http://127.0.0.1:8000/api/v1/auth/login', {{
                method: 'POST',
                headers: {{
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + idToken
                }},
                body: JSON.stringify({{ id_token: idToken }})
            }})
            .then(response => response.json())
            .then(data => {{
                document.getElementById('api-status').innerHTML = 
                    '<div style="color: green;">✅ API Test Successful!</div>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            }})
            .catch(error => {{
                document.getElementById('api-status').innerHTML = 
                    '<div style="color: red;">❌ API Test Failed: ' + error.message + '</div>';
            }});
        }}
    </script>
</body>
</html>"""
    
    # Save HTML file
    with open('token_converter.html', 'w') as f:
        f.write(html_content)
    
    print(f"\n✅ Created token_converter.html")
    print(f"\n📋 Next Steps:")
    print(f"1. Open token_converter.html in your browser")
    print(f"2. Update the Firebase config with your actual project settings")
    print(f"3. Click 'Initialize Firebase'")
    print(f"4. Click 'Convert to ID Token'")
    print(f"5. Copy the ID token and use it in your API calls")
    print(f"\n💡 The ID token is what you need for API authentication!")

def main():
    print("🔄 Firebase Custom Token to ID Token Converter")
    print("=" * 60)
    
    print("Choose conversion method:")
    print("1. Simple method (creates HTML file for manual conversion)")
    print("2. Automated method (requires Selenium/Chrome)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        simple_token_converter()
    elif choice == "2":
        print("❌ Automated method not implemented in this version")
        print("💡 Use method 1 for now")
        simple_token_converter()
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
