#!/usr/bin/env python3
"""
Script to add videos to existing properties
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from sqlalchemy.orm import Session
from app.db.session import get_db
from app.db import models

def add_videos_to_existing_properties():
    """Add sample videos to existing properties"""
    db = next(get_db())
    
    print("🎥 Adding videos to existing properties...")
    
    # Get all properties
    properties = db.query(models.Property).all()
    
    if not properties:
        print("❌ No properties found!")
        db.close()
        return
    
    # Sample video URLs (using placeholder videos)
    sample_videos = [
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4",
        "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4"
    ]
    
    video_count = 0
    
    for i, property_obj in enumerate(properties):
        # Check if property already has videos
        existing_videos = db.query(models.PropertyVideo).filter(
            models.PropertyVideo.property_id == property_obj.id
        ).count()
        
        if existing_videos > 0:
            print(f"  ⏭️  {property_obj.title} already has {existing_videos} videos")
            continue
        
        # Add 1-2 videos per property
        video_url = sample_videos[i % len(sample_videos)]
        
        try:
            video_obj = models.PropertyVideo(
                url=video_url,
                property_id=property_obj.id,
                title=f"Virtual Tour - {property_obj.title}",
                description=f"Take a virtual tour of this beautiful {property_obj.property_type.value} in {property_obj.city}"
            )
            db.add(video_obj)
            video_count += 1
            print(f"  ✅ Added video to: {property_obj.title}")
            
        except Exception as e:
            print(f"  ❌ Error adding video to {property_obj.title}: {e}")
    
    try:
        db.commit()
        print(f"\n✅ Successfully added {video_count} videos!")
    except Exception as e:
        db.rollback()
        print(f"❌ Error committing videos: {e}")
    finally:
        db.close()

def check_property_media():
    """Check media for all properties"""
    db = next(get_db())
    
    print("\n📊 Property Media Summary:")
    print("=" * 60)
    
    properties = db.query(models.Property).all()
    
    for prop in properties:
        # Count images
        image_count = db.query(models.PropertyImage).filter(
            models.PropertyImage.property_id == prop.id
        ).count()
        
        # Count videos
        video_count = db.query(models.PropertyVideo).filter(
            models.PropertyVideo.property_id == prop.id
        ).count()
        
        print(f"🏠 {prop.title}")
        print(f"   📍 {prop.city}, {prop.state}")
        print(f"   💰 ₹{prop.price:,.0f}")
        print(f"   📸 Images: {image_count}")
        print(f"   🎥 Videos: {video_count}")
        print("-" * 40)
    
    db.close()

def create_property_media_api_test():
    """Create a simple API test for property media"""
    test_html = """
<!DOCTYPE html>
<html>
<head>
    <title>Property Media Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .property { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .media-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .media-item img { width: 100%; height: 150px; object-fit: cover; border-radius: 5px; }
        .media-item video { width: 100%; height: 150px; border-radius: 5px; }
        .error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; }
        .loading { color: blue; padding: 10px; background: #e6f3ff; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🏠 Property Media Test</h1>
    <div id="content">Loading properties...</div>
    
    <script>
        async function loadProperties() {
            const contentDiv = document.getElementById('content');
            
            try {
                const response = await fetch('/api/v1/properties/');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const properties = await response.json();
                
                if (properties.length === 0) {
                    contentDiv.innerHTML = '<div class="error">No properties found!</div>';
                    return;
                }
                
                let html = `<p>Found ${properties.length} properties:</p>`;
                
                for (const property of properties) {
                    html += `
                        <div class="property">
                            <h3>${property.title}</h3>
                            <p><strong>Location:</strong> ${property.city}, ${property.state}</p>
                            <p><strong>Price:</strong> ₹${property.price.toLocaleString()}</p>
                            <p><strong>Type:</strong> ${property.property_type} | <strong>BHK:</strong> ${property.bhk}</p>
                            <p><strong>Description:</strong> ${property.description}</p>
                            
                            <h4>📸 Images:</h4>
                            <div class="media-grid" id="images-${property.id}">
                                <div class="loading">Loading images...</div>
                            </div>
                            
                            <h4>🎥 Videos:</h4>
                            <div class="media-grid" id="videos-${property.id}">
                                <div class="loading">Loading videos...</div>
                            </div>
                        </div>
                    `;
                }
                
                contentDiv.innerHTML = html;
                
                // Load media for each property
                for (const property of properties) {
                    await loadPropertyMedia(property.id);
                }
                
            } catch (error) {
                contentDiv.innerHTML = `<div class="error">Error loading properties: ${error.message}</div>`;
                console.error('Error:', error);
            }
        }
        
        async function loadPropertyMedia(propertyId) {
            // For now, just show placeholder since we don't have media endpoints yet
            const imagesDiv = document.getElementById(`images-${propertyId}`);
            const videosDiv = document.getElementById(`videos-${propertyId}`);
            
            // Placeholder images (you can replace with actual API calls)
            imagesDiv.innerHTML = `
                <div class="media-item">
                    <img src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=300" alt="Property Image">
                </div>
                <div class="media-item">
                    <img src="https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=300" alt="Property Image">
                </div>
            `;
            
            // Placeholder video
            videosDiv.innerHTML = `
                <div class="media-item">
                    <video controls>
                        <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            `;
        }
        
        // Load properties when page loads
        document.addEventListener('DOMContentLoaded', loadProperties);
    </script>
</body>
</html>
    """
    
    with open("test_property_media.html", "w") as f:
        f.write(test_html)
    
    print("✅ Created test_property_media.html")

if __name__ == "__main__":
    print("🎥 Property Video Manager")
    print("=" * 40)
    
    # Check current media
    check_property_media()
    
    # Add videos if needed
    response = input("\n❓ Would you like to add videos to properties? (y/n): ").lower()
    if response == 'y':
        add_videos_to_existing_properties()
        print("\n" + "="*40)
        check_property_media()
    
    # Create test page
    create_property_media_api_test()
    
    print("\n✅ Property media setup complete!")
    print("🌐 Test the media at: test_property_media.html")
