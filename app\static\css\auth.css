/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Auth Container */
.auth-container {
    width: 100%;
    max-width: 450px;
    position: relative;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

.register-card {
    max-width: 500px;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auth Header */
.auth-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px 30px;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.auth-header h2 {
    font-size: 1.5rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.auth-header p {
    opacity: 0.9;
    font-size: 0.95rem;
}

/* Auth Form */
.auth-form {
    padding: 40px 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group label i {
    color: #667eea;
    width: 16px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 8px;
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background: linear-gradient(90deg, #ff4757 30%, #e1e5e9 30%);
}

.password-strength.medium {
    background: linear-gradient(90deg, #ffa502 60%, #e1e5e9 60%);
}

.password-strength.strong {
    background: linear-gradient(90deg, #2ed573 100%, #e1e5e9 100%);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    color: white;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #5a67d8;
}

/* Buttons */
.auth-button {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.auth-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.auth-button.loading .button-text {
    display: none;
}

.auth-button.loading .loading-spinner {
    display: block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.google-button {
    width: 100%;
    background: white;
    color: #333;
    border: 2px solid #e1e5e9;
    padding: 12px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.google-button:hover {
    border-color: #db4437;
    background: #fafafa;
}

.google-button i {
    color: #db4437;
    font-size: 1.1rem;
}

/* Divider */
.divider {
    text-align: center;
    margin: 25px 0;
    position: relative;
    color: #666;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 25px;
    color: #666;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #5a67d8;
}

/* Error Messages */
.error-message {
    color: #ff4757;
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
    min-height: 16px;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.message {
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    border-left: 4px solid;
    animation: slideIn 0.3s ease-out;
    max-width: 350px;
}

.message.success {
    border-left-color: #2ed573;
    color: #2ed573;
}

.message.error {
    border-left-color: #ff4757;
    color: #ff4757;
}

.message.info {
    border-left-color: #3742fa;
    color: #3742fa;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Checkbox Group */
.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-group .checkbox-container {
    font-size: 0.85rem;
    line-height: 1.4;
}

.checkbox-group a {
    color: #667eea;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        max-width: 100%;
        margin: 0 10px;
    }
    
    .auth-form {
        padding: 30px 20px;
    }
    
    .auth-header {
        padding: 30px 20px 25px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .form-options {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .auth-card {
        border-radius: 15px;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .auth-header h2 {
        font-size: 1.3rem;
    }
}
